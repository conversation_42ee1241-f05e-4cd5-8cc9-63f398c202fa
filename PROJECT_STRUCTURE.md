# 项目结构说明

本项目采用MVC（Model-View-Controller）架构模式，提供清晰的代码组织和职责分离。

## 📁 目录结构

```
Gemini test 1.0/
├── app/                          # 主应用目录
│   ├── __init__.py              # 应用初始化
│   ├── app.py                   # Flask应用主文件
│   ├── main_cli.py              # 命令行主程序
│   │
│   ├── models/                  # 模型层 (Model)
│   │   ├── __init__.py
│   │   ├── image_generator.py   # 图像生成模型
│   │   ├── image_analyzer.py    # 图像分析模型
│   │   └── speech_synthesizer.py # 语音合成模型
│   │
│   ├── views/                   # 视图层 (View)
│   │   ├── __init__.py
│   │   └── templates/           # HTML模板
│   │
│   ├── controllers/             # 控制器层 (Controller)
│   │   ├── __init__.py
│   │   └── main_controller.py   # 主控制器
│   │
│   ├── config/                  # 配置管理
│   │   ├── __init__.py
│   │   └── settings.py          # 配置设置
│   │
│   └── utils/                   # 工具模块
│       ├── __init__.py
│       └── dependencies.py      # 依赖检查
│
├── templates/                   # 模板文件 (临时保留)
│   └── index.html
│
├── generated_images/            # 生成的文件目录
│   ├── *.png                   # 生成的图片
│   ├── *_analysis.txt          # 分析结果
│   └── *_audio.wav             # 语音文件
│
├── run.py                      # Web应用启动入口
├── main.py                     # 原命令行程序 (保留兼容)
├── requirements.txt            # 依赖包列表
├── .env                        # 环境变量配置
├── README.md                   # 项目说明
├── WEB_USAGE.md               # Web界面使用说明
└── PROJECT_STRUCTURE.md       # 本文件
```

## 🏗️ MVC架构说明

### Model（模型层）
负责业务逻辑和数据处理：

- **`image_generator.py`**: 图像生成业务逻辑
  - `GeminiImageGenerator` 类
  - 处理Gemini API调用
  - 图片保存和管理

- **`image_analyzer.py`**: 图像分析业务逻辑
  - `ImageAnalyzer` 类
  - 图片内容理解
  - 分析结果保存

- **`speech_synthesizer.py`**: 语音合成业务逻辑
  - `SpeechSynthesizer` 类
  - 文本转语音处理
  - 音频文件管理

### View（视图层）
负责用户界面展示：

- **`templates/index.html`**: Web界面模板
  - 响应式设计
  - 实时更新功能
  - 美观的用户界面

### Controller（控制器层）
负责协调模型和视图：

- **`main_controller.py`**: 主控制器
  - 路由管理
  - API端点处理
  - 业务逻辑协调
  - 错误处理

### Config（配置层）
负责配置管理：

- **`settings.py`**: 统一配置管理
  - 环境变量读取
  - 默认值设置
  - 配置验证

### Utils（工具层）
负责通用功能：

- **`dependencies.py`**: 依赖检查工具
  - 包安装验证
  - 版本检查
  - 自动安装

## 🚀 启动方式

### Web应用模式（推荐）
```bash
python run.py
```
访问：http://localhost:5000

### 命令行模式
```bash
# 完整流程
python app/main_cli.py

# 交互模式
python app/main_cli.py --interactive

# 兼容旧版本
python main.py
```

## 🔧 配置文件

### `.env` 文件配置
```env
# Gemini API配置
GEMINI_API_KEY=your_api_key_here

# 代理配置（可选）
HTTP_PROXY=http://127.0.0.1:7890
HTTPS_PROXY=http://127.0.0.1:7890

# TTS配置
DASHSCOPE_API_KEY=your_dashscope_key

# Flask配置
FLASK_HOST=0.0.0.0
FLASK_PORT=5000
FLASK_DEBUG=True
```

## 📦 依赖管理

### 核心依赖
- `google-generativeai`: Gemini API客户端
- `flask`: Web框架
- `pillow`: 图像处理
- `python-dotenv`: 环境变量管理
- `dashscope`: 语音合成（可选）

### 安装依赖
```bash
pip install -r requirements.txt
```

## 🎯 设计优势

1. **职责分离**: 每个层次都有明确的职责
2. **易于维护**: 模块化设计便于修改和扩展
3. **可测试性**: 各层可以独立测试
4. **可扩展性**: 容易添加新功能和模块
5. **配置集中**: 统一的配置管理
6. **错误处理**: 完善的异常处理机制

## 🔄 数据流

```
用户请求 → Controller → Model → 业务处理 → Model → Controller → View → 用户响应
```

1. 用户通过Web界面或命令行发起请求
2. Controller接收请求并验证参数
3. Controller调用相应的Model处理业务逻辑
4. Model执行具体操作（生成图片、分析、合成语音）
5. Model返回处理结果给Controller
6. Controller格式化响应并返回给View
7. View展示结果给用户

这种架构确保了代码的清晰性、可维护性和可扩展性。
