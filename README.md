# Gemini 图像生成器

这是一个使用 Google Gemini API 进行图像生成的 Python 工具。

## 功能特点

- ✅ **面向对象设计** - 使用 `GeminiImageGenerator` 类封装所有功能
- 🎨 **高质量图像生成** - 使用 Gemini 2.0 Flash 模型生成图像
- 🔧 **智能错误处理** - 完整的异常处理和错误恢复机制
- 📦 **模块化架构** - 独立的依赖检查和配置管理
- 🌐 **代理支持** - 自动检测和使用HTTP/HTTPS代理
- 📁 **自动文件管理** - 智能文件命名和目录管理
- 🖼️ **自动预览** - 生成后自动在默认查看器中打开图片
- 📝 **详细日志** - 清晰的状态提示和进度显示
- 🔄 **批量处理** - 支持批量生成多张图片

## 安装依赖

```bash
pip install -r requirements.txt
```

## 配置

### 方法1: 使用环境变量文件

1. 复制示例配置文件：
```bash
cp .env.example .env
```

2. 编辑 `.env` 文件，配置您的设置：
```
# 必需配置
GEMINI_API_KEY=your_actual_api_key_here

# 可选配置 (有默认值)
DEFAULT_MODEL=gemini-2.0-flash-preview-image-generation
FALLBACK_MODEL=gemini-2.5-flash
DEFAULT_PROMPT=八骏图
OUTPUT_PREFIX=gemini-native-image
IMAGE_FORMAT=png

# 代理配置 (如果需要)
HTTP_PROXY=http://your-proxy-server:port
HTTPS_PROXY=http://your-proxy-server:port
```

### 方法2: 直接设置环境变量

```bash
# Windows
set GEMINI_API_KEY=your_actual_api_key_here

# Linux/Mac
export GEMINI_API_KEY=your_actual_api_key_here
```

## 使用方法

### 基本使用

```python
from image_gen import GeminiImageGenerator

# 创建生成器实例
generator = GeminiImageGenerator()

# 生成图像
result = generator.generate_image("一只可爱的小猫在花园里玩耍")

# 检查结果
if result['success']:
    print(f"✅ 成功生成 {result['image_count']} 张图片")
    for img_file in result['images']:
        print(f"📁 保存文件: {img_file}")
else:
    print(f"❌ 生成失败: {result['error']}")
```

### 批量生成

```python
# 批量生成多张图片
prompts = [
    "美丽的日出风景",
    "现代城市夜景",
    "古典中国山水画"
]

generator = GeminiImageGenerator()
for prompt in prompts:
    result = generator.generate_image(prompt, show_proxy_status=False)
    if result['success']:
        print(f"✅ '{prompt}': 生成成功")
```

### 自定义 API 密钥

```python
# 使用自定义 API 密钥
generator = GeminiImageGenerator(api_key="your_custom_api_key")
result = generator.generate_image("传统中国书法艺术")
```

## GeminiImageGenerator 类

### 主要方法

- `__init__(api_key=None)` - 初始化生成器，可选择提供自定义 API 密钥
- `generate_image(prompt=None, show_proxy_status=True)` - 生成图像
  - `prompt`: 图像生成提示词（可选，默认使用配置中的提示词）
  - `show_proxy_status`: 是否显示代理状态（默认为 True）
  - 返回包含生成结果的字典

### 返回值格式

```python
{
    'success': bool,           # 是否成功
    'error': str,             # 错误信息（如果失败）
    'images': list,           # 生成的图片文件列表
    'ai_response': str,       # AI 的文本回复
    'image_count': int        # 生成的图片数量
}
```

## 运行程序

### 检查依赖

```bash
# 独立运行依赖检查
python dependencies.py
```

### 直接运行主程序

```bash
python image_gen.py
```

### 方式一：一键运行完整流程（推荐）

```bash
# 运行主程序，自动完成：图片生成 → 内容分析 → 语音合成
python main.py
```

### 方式二：分步运行

```bash
# 查看类使用示例
python class_usage_example.py

# 分析生成的图片
python image_understand.py

# 将分析结果转换为语音
python speaker.py
```

### 图片理解功能

`image_understand.py` 程序可以自动分析 `image_gen.py` 生成的图片：

- **自动查找**：从 `generated_images` 目录中自动找到最新生成的图片
- **智能分析**：使用 Gemini 2.5 Flash 模型进行详细的图片内容分析
- **详细描述**：提供图片的主要元素、颜色、风格和整体感觉的详细描述
- **自动保存**：分析结果自动保存为文本文件，与图片保存在同一目录

**使用步骤：**
1. 先运行 `python image_gen.py` 生成图片
2. 再运行 `python image_understand.py` 分析图片
3. 最后运行 `python speaker.py` 将分析结果转换为语音

### 主程序功能

`main.py` 是一个集成的主程序，提供完整的工作流程：

- **交互式界面**：友好的用户界面，引导用户输入提示词
- **自动化流程**：依次执行图片生成、内容分析、语音合成三个步骤
- **进度显示**：实时显示每个步骤的执行状态和结果
- **错误处理**：完善的错误处理机制，出现问题时会中断流程并提示
- **结果汇总**：最后展示所有生成文件的详细信息和总耗时

### 语音合成功能

`speaker.py` 程序可以将图片分析结果转换为语音：

- **自动读取**：从 `generated_images` 目录中自动读取最新的分析结果文件
- **智能截取**：自动处理文本长度，确保符合TTS API限制（500字符以内）
- **语音合成**：使用阿里云通义千问TTS模型生成高质量语音
- **自动保存**：语音文件自动保存在与图片相同的目录中

## 文件结构

```
.
├── main.py                         # 主程序（一键运行完整流程）
├── image_gen.py                    # 主要的图像生成程序 (包含 GeminiImageGenerator 类)
├── image_understand.py             # 图片理解和分析程序
├── speaker.py                      # 语音合成程序
├── dependencies.py                 # 依赖检查模块
├── class_usage_example.py          # 类使用示例
├── requirements.txt               # Python依赖
├── .env.example                   # 环境变量示例
├── .env                           # 环境变量配置文件
├── generated_images/              # 生成的图片目录
│   ├── gemini-native-image-*.png  # 生成的图片文件
│   ├── gemini-native-image-*_analysis.txt  # 图片分析结果文件
│   └── gemini-native-image-*_audio.wav     # 分析结果语音文件
└── README.md                      # 说明文档
```

## 生成的文件

程序会在指定目录（默认为 `generated_images`）中创建以下文件：

- `gemini-native-image-{序号}.png` - 生成的图像文件
- `gemini-native-image-{序号}_analysis.txt` - 图片分析结果文件（运行 `image_understand.py` 后自动生成）
- `gemini-native-image-{序号}_audio.wav` - 分析结果语音文件（运行 `speaker.py` 后自动生成）
- 图片会自动在默认查看器中打开

## 错误处理

程序包含完整的错误处理机制：

- API密钥验证
- 网络连接错误处理
- 文件保存错误处理
- 详细的日志记录

## 注意事项

1. 确保您有有效的 Google Gemini API 密钥
2. 生成图像需要网络连接
3. 生成的图像文件可能较大，请确保有足够的磁盘空间
4. API调用可能有频率限制，请合理使用

## 许可证

本项目仅供学习和研究使用。
