# Gemini 图像生成器

这是一个使用 Google Gemini API 进行图像生成的 Python 工具。

## 功能特点

- 🎨 使用 Gemini 2.0 Flash 模型生成高质量图像
- 🔧 完整的错误处理和日志记录
- 📁 自动文件管理和组织
- 🌐 支持环境变量配置
- 📝 保存生成描述和元数据
- 🔄 批量生成支持
- 🖼️ **新功能**: 图片生成后自动打开预览

## 安装依赖

```bash
pip install -r requirements.txt
```

## 配置

### 方法1: 使用环境变量文件

1. 复制示例配置文件：
```bash
cp .env.example .env
```

2. 编辑 `.env` 文件，填入您的 Gemini API 密钥：
```
GEMINI_API_KEY=your_actual_api_key_here
```

### 方法2: 直接设置环境变量

```bash
# Windows
set GEMINI_API_KEY=your_actual_api_key_here

# Linux/Mac
export GEMINI_API_KEY=your_actual_api_key_here
```

## 使用方法

### 基本使用

```python
from image_gen import GeminiImageGenerator

# 创建生成器
generator = GeminiImageGenerator()

# 生成图像
result = generator.generate_image(
    prompt="一只可爱的小猫在花园里玩耍，卡通风格",
    filename="cute_cat"
)

print(f"生成的文件: {result['files_saved']}")
```

### 高级使用

```python
# 自定义输出目录和设置
result = generator.generate_image(
    prompt="山水画，中国传统风格，高山流水",
    output_dir="my_images",
    filename="landscape_001",
    save_text_response=True,
    auto_open=True  # 自动打开生成的图片
)
```

### 自动打开功能

```python
# 生成图片并自动打开（默认行为）
result = generator.generate_image(
    prompt="美丽的风景",
    auto_open=True
)

# 生成图片但不自动打开
result = generator.generate_image(
    prompt="美丽的风景",
    auto_open=False
)

# 批量生成时，可以选择只打开最后一张
for i, prompt in enumerate(prompts):
    auto_open = (i == len(prompts) - 1)  # 只有最后一张自动打开
    generator.generate_image(prompt=prompt, auto_open=auto_open)
```

### 运行示例

```bash
python example_usage.py
```

### 演示自动打开功能

```bash
python demo_auto_open.py
```

### 检查依赖

```bash
# 独立运行依赖检查
python dependencies.py

# 查看依赖检查使用示例
python example_with_dependencies.py
```

### 直接运行主程序

```bash
python image_gen.py
```

## 文件结构

```
.
├── image_gen.py                    # 主要的图像生成程序
├── dependencies.py                 # 依赖检查模块
├── config.py                      # 配置管理
├── example_usage.py               # 使用示例
├── example_with_dependencies.py   # 依赖检查使用示例
├── demo_auto_open.py              # 自动打开功能演示
├── requirements.txt               # Python依赖
├── .env.example                   # 环境变量示例
└── README.md                      # 说明文档
```

## 生成的文件

程序会在指定目录（默认为 `generated_images`）中创建以下文件：

- `{filename}.jpg` - 生成的图像文件
- `{filename}_description.txt` - AI生成的描述文本（可选）

## 错误处理

程序包含完整的错误处理机制：

- API密钥验证
- 网络连接错误处理
- 文件保存错误处理
- 详细的日志记录

## 注意事项

1. 确保您有有效的 Google Gemini API 密钥
2. 生成图像需要网络连接
3. 生成的图像文件可能较大，请确保有足够的磁盘空间
4. API调用可能有频率限制，请合理使用

## 许可证

本项目仅供学习和研究使用。
