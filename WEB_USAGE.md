# Web应用使用指南

## 🌐 启动Web应用

1. **启动服务器**
   ```bash
   python web_app.py
   ```

2. **访问应用**
   - 在浏览器中打开：http://localhost:5000
   - 或者使用局域网IP访问（显示在启动信息中）

## 🎨 使用流程

### 步骤1：生成图片
1. 在"生成图片"区域的文本框中输入提示词
   - 例如：`美丽的夕阳风景`、`可爱的小猫`、`未来城市`
2. 点击 **🎨 生成图片** 按钮
3. 等待图片生成完成（通常需要10-30秒）
4. 生成的图片会自动显示在页面上

### 步骤2：理解图片
1. 图片生成成功后，**🔍 理解图片** 按钮会自动启用
2. 点击该按钮开始分析图片内容
3. 等待AI分析完成（通常需要5-15秒）
4. 分析结果会显示在页面上，包含图片的详细描述

### 步骤3：生成语音
1. 图片分析完成后，**🔊 生成语音** 按钮会自动启用
2. 点击该按钮将分析结果转换为语音
3. 等待语音生成完成（通常需要5-10秒）
4. 生成的音频播放器会出现在页面上

### 步骤4：播放语音
1. 语音生成完成后，可以使用内置的音频播放器
2. 点击 **▶️ 播放语音** 按钮或直接使用播放器控件
3. 支持暂停、快进、音量调节等功能

## 🔧 功能特点

### 状态指示器
- **灰色圆点**：步骤未开始
- **黄色圆点**：步骤正在进行中
- **绿色圆点**：步骤已完成
- **红色圆点**：步骤出现错误

### 自动状态恢复
- 页面刷新后会自动检测之前生成的文件
- 如果存在历史文件，相应的步骤会显示为已完成状态
- 可以直接从任何已完成的步骤继续操作

### 错误处理
- 每个步骤都有完善的错误处理机制
- 出现错误时会显示详细的错误信息
- 错误消息会在5秒后自动消失

### 响应式设计
- 支持桌面和移动设备
- 界面会根据屏幕尺寸自动调整
- 在各种设备上都有良好的用户体验

## 📁 文件管理

所有生成的文件都保存在 `generated_images/` 目录中：
- **图片文件**：`gemini-native-image-*.png`
- **分析文件**：`gemini-native-image-*_analysis.txt`
- **音频文件**：`gemini-native-image-*_audio.wav`

## 🚨 注意事项

1. **网络连接**：需要稳定的网络连接访问AI服务
2. **API配置**：确保 `.env` 文件中的API密钥配置正确
3. **浏览器兼容性**：推荐使用现代浏览器（Chrome、Firefox、Safari、Edge）
4. **文件大小**：生成的音频文件可能较大（通常2-5MB）
5. **并发限制**：同时只能处理一个请求，请等待当前步骤完成

## 🔧 故障排除

### 图片生成失败
- 检查网络连接
- 确认Gemini API密钥是否正确
- 检查代理设置（如果使用代理）

### 图片分析失败
- 确保图片已成功生成
- 检查Gemini API配额是否充足

### 语音生成失败
- 确认通义千问API密钥是否正确
- 检查分析文件是否存在
- 确认网络连接稳定

### 页面无法访问
- 确认Web应用是否正在运行
- 检查端口5000是否被占用
- 尝试使用 `127.0.0.1:5000` 而不是 `localhost:5000`

## 🎯 使用技巧

1. **提示词优化**：使用具体、生动的描述词可以获得更好的图片效果
2. **批量处理**：可以连续生成多张图片，系统会自动处理文件命名
3. **结果保存**：所有生成的文件都会自动保存，可以随时查看历史结果
4. **音频下载**：可以右键点击音频播放器选择"另存为"下载音频文件

## 📞 技术支持

如果遇到问题，请检查：
1. 控制台输出信息（运行 `python web_app.py` 的终端）
2. 浏览器开发者工具的控制台信息
3. `.env` 文件配置是否正确
4. 网络连接是否正常
