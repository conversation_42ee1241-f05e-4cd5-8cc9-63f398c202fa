"""
Flask应用主文件
基于MVC架构的AI图片生成与分析语音合成系统
"""
from flask import Flask
from app.controllers.main_controller import MainController
from app.config.settings import Config


def create_app():
    """创建Flask应用实例"""
    app = Flask(__name__, 
                template_folder='../templates',  # 暂时使用原模板目录
                static_folder='../generated_images',  # 静态文件目录
                static_url_path='/static/images')
    
    # 配置应用
    app.config['SECRET_KEY'] = 'your-secret-key-here'
    app.config['DEBUG'] = Config.get_flask_debug()
    
    # 初始化控制器
    controller = MainController(app)
    
    return app


def main():
    """主函数"""
    print("🌐 启动Web应用...")
    print(f"📱 访问地址：http://localhost:{Config.get_flask_port()}")
    print("🎨 AI图片生成与分析语音合成系统")
    
    # 创建应用
    app = create_app()
    
    # 启动应用
    app.run(
        host=Config.get_flask_host(),
        port=Config.get_flask_port(),
        debug=Config.get_flask_debug()
    )


if __name__ == '__main__':
    main()
