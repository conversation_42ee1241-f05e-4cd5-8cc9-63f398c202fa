"""
配置管理模块
负责管理应用程序的所有配置参数
"""
import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

try:
    from dotenv import load_dotenv
    # 加载环境变量
    load_dotenv()
except ImportError:
    print("❌ 未安装 python-dotenv，请运行: pip install python-dotenv")
    sys.exit(1)


class Config:
    """配置管理类"""

    @staticmethod
    def get_api_key():
        """获取 API 密钥"""
        return os.getenv("GEMINI_API_KEY", "AIzaSyDw3Z1JLckTkAc47hxVb8PuDiIr80zSbbs")

    @staticmethod
    def get_default_model():
        """获取默认模型"""
        return os.getenv("DEFAULT_MODEL", "gemini-2.0-flash-preview-image-generation")

    @staticmethod
    def get_fallback_model():
        """获取备用模型"""
        return os.getenv("FALLBACK_MODEL", "gemini-2.5-flash")

    @staticmethod
    def get_default_prompt():
        """获取默认提示词"""
        return os.getenv("DEFAULT_PROMPT", "八骏图")

    @staticmethod
    def get_output_prefix():
        """获取输出文件前缀"""
        return os.getenv("OUTPUT_PREFIX", "gemini-native-image")

    @staticmethod
    def get_image_format():
        """获取图片格式"""
        return os.getenv("IMAGE_FORMAT", "png")

    @staticmethod
    def get_output_dir():
        """获取输出目录"""
        return os.getenv("OUTPUT_DIR", "generated_images")

    @staticmethod
    def get_proxy_info():
        """获取代理信息"""
        return {
            'http_proxy': os.getenv("HTTP_PROXY"),
            'https_proxy': os.getenv("HTTPS_PROXY"),
            'proxy_url': os.getenv("PROXY_URL")
        }

    @staticmethod
    def has_proxy():
        """检查是否配置了代理"""
        proxy_info = Config.get_proxy_info()
        return any(proxy_info.values())

    # TTS相关配置
    @staticmethod
    def get_dashscope_api_key():
        """获取通义千问API密钥"""
        return os.getenv("DASHSCOPE_API_KEY")

    @staticmethod
    def get_tts_model():
        """获取TTS模型"""
        return os.getenv("TTS_MODEL", "sambert-zhichu-v1")

    @staticmethod
    def get_tts_voice():
        """获取TTS语音"""
        return os.getenv("TTS_VOICE", "zhixiaoxia")

    @staticmethod
    def get_tts_format():
        """获取TTS格式"""
        return os.getenv("TTS_FORMAT", "wav")

    @staticmethod
    def get_tts_sample_rate():
        """获取TTS采样率"""
        return int(os.getenv("TTS_SAMPLE_RATE", "16000"))

    # Flask应用配置
    @staticmethod
    def get_flask_host():
        """获取Flask主机地址"""
        return os.getenv("FLASK_HOST", "0.0.0.0")

    @staticmethod
    def get_flask_port():
        """获取Flask端口"""
        return int(os.getenv("FLASK_PORT", "5000"))

    @staticmethod
    def get_flask_debug():
        """获取Flask调试模式"""
        return os.getenv("FLASK_DEBUG", "True").lower() == "true"
