"""
主控制器
负责处理Web应用的路由和业务逻辑协调
"""
import os
import glob
from flask import Flask, render_template, request, jsonify, send_from_directory
from app.models.image_generator import GeminiImageGenerator
from app.models.image_analyzer import ImageAnalyzer
from app.models.speech_synthesizer import SpeechSynthesizer
from app.config.settings import Config


class MainController:
    """主控制器类"""

    def __init__(self, app):
        """
        初始化控制器
        
        Args:
            app: Flask应用实例
        """
        self.app = app
        self.image_generator = GeminiImageGenerator()
        self.image_analyzer = ImageAnalyzer()
        self.speech_synthesizer = SpeechSynthesizer()
        
        # 注册路由
        self._register_routes()

    def _register_routes(self):
        """注册所有路由"""
        self.app.add_url_rule('/', 'index', self.index)
        self.app.add_url_rule('/api/status', 'api_status', self.api_status)
        self.app.add_url_rule('/api/generate_image', 'api_generate_image', self.api_generate_image, methods=['POST'])
        self.app.add_url_rule('/api/analyze_image', 'api_analyze_image', self.api_analyze_image, methods=['POST'])
        self.app.add_url_rule('/api/generate_speech', 'api_generate_speech', self.api_generate_speech, methods=['POST'])
        self.app.add_url_rule('/api/check_latest_image', 'api_check_latest_image', self.api_check_latest_image)
        self.app.add_url_rule('/static/images/<filename>', 'serve_image', self.serve_image)
        self.app.add_url_rule('/static/audio/<filename>', 'serve_audio', self.serve_audio)

    def index(self):
        """首页路由"""
        return render_template('index.html')

    def api_status(self):
        """获取应用状态API"""
        try:
            # 检查最新文件状态
            latest_image = self._get_latest_file_info('image')
            latest_analysis = self._get_latest_file_info('analysis')
            latest_audio = self._get_latest_file_info('audio')
            
            return jsonify({
                'success': True,
                'status': 'running',
                'latest_image': latest_image,
                'latest_analysis': latest_analysis,
                'latest_audio': latest_audio
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500

    def api_generate_image(self):
        """图片生成API"""
        try:
            data = request.get_json()
            prompt = data.get('prompt', '').strip()
            
            if not prompt:
                return jsonify({
                    'success': False,
                    'error': '请提供图片生成提示词'
                }), 400
            
            # 生成图片
            success = self.image_generator.generate_image(prompt, show_proxy_status=False)
            
            if success:
                # 获取最新生成的图片
                latest_image = self._get_latest_file_info('image')
                
                return jsonify({
                    'success': True,
                    'message': '图片生成成功',
                    'latest_image': latest_image
                })
            else:
                return jsonify({
                    'success': False,
                    'error': '图片生成失败'
                }), 500
                
        except Exception as e:
            return jsonify({
                'success': False,
                'error': f'生成图片时出错: {str(e)}'
            }), 500

    def api_analyze_image(self):
        """图片分析API"""
        try:
            # 分析最新生成的图片
            analysis_result = self.image_analyzer.analyze_image()
            
            if analysis_result:
                return jsonify({
                    'success': True,
                    'message': '图片分析完成',
                    'analysis': analysis_result,
                    'latest_analysis': self._get_latest_file_info('analysis')
                })
            else:
                return jsonify({
                    'success': False,
                    'error': '图片分析失败'
                }), 500
                
        except Exception as e:
            return jsonify({
                'success': False,
                'error': f'分析图片时出错: {str(e)}'
            }), 500

    def api_generate_speech(self):
        """语音生成API"""
        try:
            # 生成语音
            audio_path = self.speech_synthesizer.synthesize_speech()
            
            if audio_path:
                return jsonify({
                    'success': True,
                    'message': '语音生成成功',
                    'latest_audio': self._get_latest_file_info('audio')
                })
            else:
                return jsonify({
                    'success': False,
                    'error': '语音生成失败'
                }), 500
                
        except Exception as e:
            return jsonify({
                'success': False,
                'error': f'生成语音时出错: {str(e)}'
            }), 500

    def api_check_latest_image(self):
        """检查最新图片API - 用于实时更新"""
        try:
            # 获取最新图片
            output_dir = Config.get_output_dir()
            image_pattern = os.path.join(output_dir, f"{Config.get_output_prefix()}-*.{Config.get_image_format()}")
            image_files = glob.glob(image_pattern)
            
            if image_files:
                latest_image = max(image_files, key=os.path.getctime)
                image_filename = os.path.basename(latest_image)
                
                return jsonify({
                    'success': True,
                    'has_image': True,
                    'image_path': f'/static/images/{image_filename}',
                    'image_filename': image_filename,
                    'timestamp': os.path.getctime(latest_image)
                })
            else:
                return jsonify({
                    'success': True,
                    'has_image': False
                })
                
        except Exception as e:
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500

    def serve_image(self, filename):
        """提供图片文件服务"""
        return send_from_directory(Config.get_output_dir(), filename)

    def serve_audio(self, filename):
        """提供音频文件服务"""
        return send_from_directory(Config.get_output_dir(), filename)

    def _get_latest_file_info(self, file_type):
        """获取最新文件信息"""
        try:
            output_dir = Config.get_output_dir()
            prefix = Config.get_output_prefix()
            
            if file_type == 'image':
                pattern = os.path.join(output_dir, f"{prefix}-*.{Config.get_image_format()}")
            elif file_type == 'analysis':
                pattern = os.path.join(output_dir, f"{prefix}-*_analysis.txt")
            elif file_type == 'audio':
                pattern = os.path.join(output_dir, f"{prefix}-*_audio.{Config.get_tts_format()}")
            else:
                return None
            
            files = glob.glob(pattern)
            if files:
                latest_file = max(files, key=os.path.getctime)
                filename = os.path.basename(latest_file)
                return filename
            
            return None
            
        except Exception:
            return None
