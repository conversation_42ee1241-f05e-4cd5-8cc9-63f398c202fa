"""
命令行主程序
提供完整的图片生成、分析和语音合成工作流
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.models.image_generator import GeminiImageGenerator
from app.models.image_analyzer import ImageAnalyzer
from app.models.speech_synthesizer import SpeechSynthesizer
from app.config.settings import Config


def main():
    """主函数 - 完整的工作流程"""
    print("🎨 AI图片生成与分析语音合成系统")
    print("=" * 50)
    
    # 初始化组件
    image_generator = GeminiImageGenerator()
    image_analyzer = ImageAnalyzer()
    speech_synthesizer = SpeechSynthesizer()
    
    try:
        # 步骤1: 生成图片
        print("\n📝 步骤1: 图片生成")
        print("-" * 30)
        
        # 获取用户输入的提示词
        prompt = input("请输入图片生成提示词 (直接回车使用默认): ").strip()
        if not prompt:
            prompt = Config.get_default_prompt()
            print(f"使用默认提示词: {prompt}")
        
        print(f"\n🎨 开始生成图片...")
        success = image_generator.generate_image(prompt)
        
        if not success:
            print("❌ 图片生成失败，程序退出")
            return False
        
        print("✅ 图片生成完成！")
        
        # 步骤2: 分析图片
        print("\n🔍 步骤2: 图片分析")
        print("-" * 30)
        
        input("按回车键开始分析图片...")
        
        print("🤖 开始分析图片...")
        analysis_result = image_analyzer.analyze_image()
        
        if not analysis_result:
            print("❌ 图片分析失败")
            return False
        
        print("✅ 图片分析完成！")
        print(f"📄 分析结果: {analysis_result[:200]}...")
        
        # 步骤3: 生成语音
        print("\n🔊 步骤3: 语音合成")
        print("-" * 30)
        
        input("按回车键开始生成语音...")
        
        print("🎵 开始生成语音...")
        audio_path = speech_synthesizer.synthesize_speech()
        
        if not audio_path:
            print("❌ 语音生成失败")
            return False
        
        print("✅ 语音生成完成！")
        
        # 完成
        print("\n🎉 完整流程执行完成！")
        print("=" * 50)
        print("📁 生成的文件:")
        
        # 显示生成的文件
        latest_image = image_generator.get_latest_image()
        if latest_image:
            print(f"🖼️  图片: {latest_image}")
        
        latest_analysis = image_analyzer.get_latest_analysis()
        if latest_analysis:
            analysis_file = latest_image.replace('.png', '_analysis.txt') if latest_image else None
            if analysis_file and os.path.exists(analysis_file):
                print(f"📄 分析: {analysis_file}")
        
        latest_audio = speech_synthesizer.get_latest_audio()
        if latest_audio:
            print(f"🎵 语音: {latest_audio}")
        
        return True
        
    except KeyboardInterrupt:
        print("\n\n👋 用户取消操作")
        return False
    except Exception as e:
        print(f"\n❌ 程序执行出错: {e}")
        return False


def interactive_mode():
    """交互模式"""
    print("🎮 交互模式")
    print("=" * 30)
    
    # 初始化组件
    image_generator = GeminiImageGenerator()
    image_analyzer = ImageAnalyzer()
    speech_synthesizer = SpeechSynthesizer()
    
    while True:
        print("\n请选择操作:")
        print("1. 生成图片")
        print("2. 分析图片")
        print("3. 生成语音")
        print("4. 完整流程")
        print("0. 退出")
        
        try:
            choice = input("\n请输入选项 (0-4): ").strip()
            
            if choice == '0':
                print("👋 再见！")
                break
            elif choice == '1':
                prompt = input("请输入图片生成提示词: ").strip()
                if prompt:
                    image_generator.generate_image(prompt)
                else:
                    print("❌ 提示词不能为空")
            elif choice == '2':
                image_analyzer.analyze_image()
            elif choice == '3':
                speech_synthesizer.synthesize_speech()
            elif choice == '4':
                main()
            else:
                print("❌ 无效选项，请重新选择")
                
        except KeyboardInterrupt:
            print("\n\n👋 用户取消操作")
            break
        except Exception as e:
            print(f"❌ 操作失败: {e}")


if __name__ == "__main__":
    """程序入口"""
    if len(sys.argv) > 1 and sys.argv[1] == '--interactive':
        interactive_mode()
    else:
        main()
