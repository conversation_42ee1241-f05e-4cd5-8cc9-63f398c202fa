"""
图像分析模型
负责处理图像理解和分析的业务逻辑
"""
import os
import sys
import glob

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

try:
    import google.generativeai as genai
    from PIL import Image
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    print("请运行: pip install google-generativeai pillow")
    sys.exit(1)

from app.config.settings import Config


class ImageAnalyzer:
    """图像分析器类"""

    def __init__(self, api_key=None):
        """
        初始化图像分析器
        
        Args:
            api_key (str, optional): API密钥，如果不提供则从配置中获取
        """
        self.api_key = api_key or Config.get_api_key()
        self.output_dir = Config.get_output_dir()
        self.output_prefix = Config.get_output_prefix()
        self.image_format = Config.get_image_format()

    def _initialize_client(self):
        """初始化Gemini客户端"""
        try:
            # 配置代理（如果有）
            if Config.has_proxy():
                proxy_info = Config.get_proxy_info()
                for key, value in proxy_info.items():
                    if value:
                        os.environ[key.upper()] = value

            # 配置API密钥
            genai.configure(api_key=self.api_key)
            return True
            
        except Exception as e:
            print(f"❌ 初始化客户端失败: {e}")
            return False

    def analyze_image(self, image_path=None, prompt="请简洁地描述这张图片的内容，包括主要元素、颜色、风格和整体感觉。请控制在400字以内，语言简练。"):
        """
        分析图片内容
        
        Args:
            image_path (str, optional): 图片路径，如果不提供则使用最新生成的图片
            prompt (str): 分析提示词
            
        Returns:
            str: 分析结果文本，失败时返回None
        """
        if not self._initialize_client():
            return None

        # 如果没有指定图片路径，使用最新生成的图片
        if not image_path:
            image_path = self._get_latest_image()
            if not image_path:
                print("❌ 没有找到可分析的图片")
                return None

        try:
            print(f"🔍 开始分析图片: {image_path}")
            
            # 检查文件是否存在
            if not os.path.exists(image_path):
                print(f"❌ 图片文件不存在: {image_path}")
                return None

            # 加载图片
            image = Image.open(image_path)
            
            # 使用Gemini 2.5 Flash模型进行图片理解
            model = genai.GenerativeModel(Config.get_fallback_model())
            
            print("🤖 发送图片分析请求...")
            response = model.generate_content([prompt, image])
            
            if response.text:
                analysis_text = response.text.strip()
                print("✅ 图片分析完成")
                
                # 保存分析结果
                self._save_analysis_result(image_path, analysis_text)
                
                return analysis_text
            else:
                print("❌ 分析响应为空")
                return None
                
        except Exception as e:
            print(f"❌ 分析图片时出错: {e}")
            return None

    def _get_latest_image(self):
        """获取最新生成的图片路径"""
        pattern = os.path.join(self.output_dir, f"{self.output_prefix}-*.{self.image_format}")
        image_files = glob.glob(pattern)
        
        if image_files:
            return max(image_files, key=os.path.getctime)
        return None

    def _save_analysis_result(self, image_path, analysis_text):
        """保存分析结果到文件"""
        try:
            # 生成分析结果文件名
            base_name = os.path.splitext(os.path.basename(image_path))[0]
            analysis_filename = f"{base_name}_analysis.txt"
            analysis_path = os.path.join(self.output_dir, analysis_filename)
            
            # 保存分析结果
            with open(analysis_path, 'w', encoding='utf-8') as f:
                f.write(analysis_text)
            
            print(f"💾 分析结果已保存到: {analysis_path}")
            return analysis_path
            
        except Exception as e:
            print(f"⚠️ 保存分析结果失败: {e}")
            return None

    def get_latest_analysis(self):
        """获取最新的分析结果"""
        pattern = os.path.join(self.output_dir, f"{self.output_prefix}-*_analysis.txt")
        analysis_files = glob.glob(pattern)
        
        if analysis_files:
            latest_file = max(analysis_files, key=os.path.getctime)
            try:
                with open(latest_file, 'r', encoding='utf-8') as f:
                    return f.read().strip()
            except Exception as e:
                print(f"❌ 读取分析文件失败: {e}")
                return None
        return None
