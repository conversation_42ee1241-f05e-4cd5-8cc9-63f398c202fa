"""
图像生成模型
负责处理图像生成的业务逻辑
"""
import os
import sys
import base64

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

try:
    from google import genai
    from google.genai import types
except ImportError:
    print("❌ 未安装 google-generativeai，请运行: pip install google-generativeai")
    sys.exit(1)

from app.config.settings import Config
from app.utils.dependencies import check_dependencies


class GeminiImageGenerator:
    """Google Gemini 图像生成器类"""

    def __init__(self, api_key=None):
        """
        初始化图像生成器
        
        Args:
            api_key (str, optional): API密钥，如果不提供则从配置中获取
        """
        self.api_key = api_key or Config.get_api_key()
        self.client = None
        self.output_dir = Config.get_output_dir()
        self.output_prefix = Config.get_output_prefix()
        self.image_format = Config.get_image_format()
        
        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)

    def _initialize_client(self, show_proxy_status=True):
        """初始化Gemini客户端"""
        try:
            # 检查依赖
            check_dependencies()
            
            # 配置代理（如果有）
            if Config.has_proxy():
                proxy_info = Config.get_proxy_info()
                if show_proxy_status:
                    print(f"🌐 检测到代理配置: {proxy_info}")
                
                # 设置代理环境变量
                for key, value in proxy_info.items():
                    if value:
                        os.environ[key.upper()] = value

            # 配置API密钥
            genai.configure(api_key=self.api_key)
            
            print("✅ Gemini 客户端初始化成功")
            return True
            
        except Exception as e:
            print(f"❌ 初始化失败: {e}")
            return False

    def generate_image(self, prompt, show_proxy_status=True):
        """
        生成图片
        
        Args:
            prompt (str): 图片生成提示词
            show_proxy_status (bool): 是否显示代理状态
            
        Returns:
            bool: 生成是否成功
        """
        if not self._initialize_client(show_proxy_status):
            return False

        try:
            print("🤖 发送图片生成请求...")
            print(f"📝 提示词: {prompt}")
            
            # 尝试使用主要模型
            model_name = Config.get_default_model()
            print(f"🔄 尝试使用 {model_name} 模型...")
            
            try:
                model = genai.GenerativeModel(model_name)
                response = model.generate_content(prompt)
                
                print("✅ 收到响应，处理结果...")
                print(f"📝 AI 回复: {response.text}")
                
                # 处理生成的图片
                if hasattr(response, '_result') and hasattr(response._result, 'candidates'):
                    candidates = response._result.candidates
                    if candidates and len(candidates) > 0:
                        return self._process_candidates(candidates)
                
                print("❌ 响应中没有找到图片数据")
                return False
                
            except Exception as model_error:
                print(f"⚠️ 主要模型失败: {model_error}")
                print("🔄 尝试使用备用模型...")
                
                # 尝试备用模型
                fallback_model = Config.get_fallback_model()
                model = genai.GenerativeModel(fallback_model)
                response = model.generate_content(f"请生成一张图片: {prompt}")
                
                print("✅ 备用模型响应成功")
                return True
                
        except Exception as e:
            print(f"❌ 生成图片时出错: {e}")
            return False

    def _process_candidates(self, candidates):
        """处理候选结果"""
        image_count = 0
        
        for i, candidate in enumerate(candidates):
            if hasattr(candidate, 'content') and hasattr(candidate.content, 'parts'):
                for part in candidate.content.parts:
                    if hasattr(part, 'inline_data'):
                        image_count += 1
                        print(f"🖼️ 处理生成的图片 {image_count}...")
                        
                        # 解码base64图片数据
                        image_data = base64.b64decode(part.inline_data.data)
                        
                        # 生成文件名
                        filename = f"{self.output_prefix}-{image_count}.{self.image_format}"
                        filepath = os.path.join(self.output_dir, filename)
                        
                        # 保存图片
                        with open(filepath, 'wb') as f:
                            f.write(image_data)
                        
                        print(f"💾 图片已保存为: {filepath}")
                        
                        # 尝试打开图片
                        self._open_image(filepath)
        
        if image_count > 0:
            print(f"🎉 成功生成并保存了 {image_count} 张图片！")
            return True
        else:
            print("❌ 没有找到可处理的图片数据")
            return False

    def _open_image(self, filepath):
        """尝试在默认查看器中打开图片"""
        try:
            if os.name == 'nt':  # Windows
                os.startfile(filepath)
            elif os.name == 'posix':  # macOS and Linux
                os.system(f'open "{filepath}"' if os.uname().sysname == 'Darwin' else f'xdg-open "{filepath}"')
            print("🖼️ 图片已在默认查看器中打开")
        except Exception as e:
            print(f"⚠️ 无法自动打开图片: {e}")

    def get_latest_image(self):
        """获取最新生成的图片路径"""
        import glob
        
        pattern = os.path.join(self.output_dir, f"{self.output_prefix}-*.{self.image_format}")
        image_files = glob.glob(pattern)
        
        if image_files:
            return max(image_files, key=os.path.getctime)
        return None
