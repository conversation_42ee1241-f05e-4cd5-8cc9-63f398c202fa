"""
语音合成模型
负责处理文本转语音的业务逻辑
"""
import os
import sys
import glob
import requests

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from app.config.settings import Config


class SpeechSynthesizer:
    """语音合成器类"""

    def __init__(self):
        """初始化语音合成器"""
        self.api_key = Config.get_dashscope_api_key()
        self.output_dir = Config.get_output_dir()
        self.output_prefix = Config.get_output_prefix()
        
        # TTS配置
        self.model = Config.get_tts_model()
        self.voice = Config.get_tts_voice()
        self.format = Config.get_tts_format()
        self.sample_rate = Config.get_tts_sample_rate()

    def synthesize_speech(self, text=None):
        """
        合成语音
        
        Args:
            text (str, optional): 要合成的文本，如果不提供则使用最新的分析结果
            
        Returns:
            str: 生成的音频文件路径，失败时返回None
        """
        if not self.api_key:
            print("❌ 未配置通义千问API密钥")
            return None

        # 如果没有提供文本，读取最新的分析结果
        if not text:
            text = self._get_latest_analysis_text()
            if not text:
                print("❌ 没有找到可合成的文本内容")
                return None

        try:
            print("🔊 开始生成语音...")
            print(f"📖 文本内容: {text[:100]}..." if len(text) > 100 else f"📖 文本内容: {text}")
            
            # 处理文本长度
            processed_text = self._process_text_length(text)
            print(f"📏 处理后文本长度: {len(processed_text)} 字符")
            
            # 调用通义千问TTS API
            audio_data = self._call_tts_api(processed_text)
            if not audio_data:
                return None
            
            # 保存音频文件
            audio_path = self._save_audio_file(audio_data)
            if audio_path:
                print(f"🎵 语音文件已保存: {audio_path}")
                return audio_path
            
            return None
            
        except Exception as e:
            print(f"❌ 生成语音时出错: {e}")
            return None

    def _get_latest_analysis_text(self):
        """获取最新的分析结果文本"""
        pattern = os.path.join(self.output_dir, f"{self.output_prefix}-*_analysis.txt")
        analysis_files = glob.glob(pattern)
        
        if analysis_files:
            latest_file = max(analysis_files, key=os.path.getctime)
            try:
                with open(latest_file, 'r', encoding='utf-8') as f:
                    return f.read().strip()
            except Exception as e:
                print(f"❌ 读取分析文件失败: {e}")
                return None
        return None

    def _process_text_length(self, text, max_length=500):
        """处理文本长度，确保不超过限制"""
        if len(text) <= max_length:
            return text
        
        # 尝试在句号处截断
        sentences = text.split('。')
        result = ""
        
        for sentence in sentences:
            if len(result + sentence + '。') <= max_length:
                result += sentence + '。'
            else:
                break
        
        # 如果没有找到合适的截断点，直接截断
        if not result:
            result = text[:max_length-3] + "..."
        
        return result

    def _call_tts_api(self, text):
        """调用通义千问TTS API"""
        try:
            import dashscope
            from dashscope.audio.tts import SpeechSynthesizer as DashScopeTTS
            
            # 设置API密钥
            dashscope.api_key = self.api_key
            
            # 调用TTS API
            response = DashScopeTTS.call(
                model=self.model,
                text=text,
                voice=self.voice,
                format=self.format,
                sample_rate=self.sample_rate
            )
            
            if response.status_code == 200:
                print("✅ TTS API调用成功")
                return response.content
            else:
                print(f"❌ TTS API调用失败: {response.status_code}")
                return None
                
        except ImportError:
            print("❌ 未安装dashscope库，请运行: pip install dashscope")
            return None
        except Exception as e:
            print(f"❌ TTS API调用出错: {e}")
            return None

    def _save_audio_file(self, audio_data):
        """保存音频文件"""
        try:
            # 生成音频文件名
            latest_image = self._get_latest_image_base_name()
            if latest_image:
                audio_filename = f"{latest_image}_audio.{self.format}"
            else:
                audio_filename = f"{self.output_prefix}-audio.{self.format}"
            
            audio_path = os.path.join(self.output_dir, audio_filename)
            
            # 保存音频数据
            with open(audio_path, 'wb') as f:
                f.write(audio_data)
            
            return audio_path
            
        except Exception as e:
            print(f"❌ 保存音频文件失败: {e}")
            return None

    def _get_latest_image_base_name(self):
        """获取最新图片的基础文件名"""
        pattern = os.path.join(self.output_dir, f"{self.output_prefix}-*.png")
        image_files = glob.glob(pattern)
        
        if image_files:
            latest_image = max(image_files, key=os.path.getctime)
            return os.path.splitext(os.path.basename(latest_image))[0]
        return None

    def get_latest_audio(self):
        """获取最新的音频文件路径"""
        pattern = os.path.join(self.output_dir, f"{self.output_prefix}-*_audio.{self.format}")
        audio_files = glob.glob(pattern)
        
        if audio_files:
            return max(audio_files, key=os.path.getctime)
        return None
