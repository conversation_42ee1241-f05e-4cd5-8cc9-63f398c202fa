"""
GeminiImageGenerator 类使用示例
展示如何使用面向对象的方式生成图像
"""

from image_gen import GeminiImageGenerator, Config
from dependencies import check_dependencies

def basic_usage_example():
    """基本使用示例"""
    print("📋 基本使用示例")
    print("=" * 30)
    
    # 检查依赖
    if not check_dependencies():
        print("❌ 依赖检查失败")
        return
    
    try:
        # 创建生成器实例
        generator = GeminiImageGenerator()
        
        # 生成图像
        result = generator.generate_image("一只可爱的小猫在花园里玩耍")
        
        # 处理结果
        if result['success']:
            print(f"✅ 成功生成 {result['image_count']} 张图片")
            for img_file in result['images']:
                print(f"📁 保存文件: {img_file}")
        else:
            print(f"❌ 生成失败: {result['error']}")
            
    except Exception as e:
        print(f"❌ 出现错误: {e}")

def batch_generation_example():
    """批量生成示例"""
    print("\n📋 批量生成示例")
    print("=" * 30)
    
    prompts = [
        "美丽的日出风景",
        "现代城市夜景",
        "古典中国山水画",
        "科幻未来世界"
    ]
    
    try:
        # 创建生成器实例
        generator = GeminiImageGenerator()
        
        results = []
        for i, prompt in enumerate(prompts, 1):
            print(f"\n🔄 生成第 {i} 张图片...")
            result = generator.generate_image(prompt, show_proxy_status=False)
            results.append({
                'prompt': prompt,
                'result': result
            })
        
        # 汇总结果
        print("\n📊 批量生成结果汇总:")
        total_images = 0
        for item in results:
            prompt = item['prompt']
            result = item['result']
            if result['success']:
                count = result['image_count']
                total_images += count
                print(f"✅ '{prompt}': {count} 张图片")
            else:
                print(f"❌ '{prompt}': 失败 - {result['error']}")
        
        print(f"\n🎉 总共成功生成 {total_images} 张图片")
        
    except Exception as e:
        print(f"❌ 批量生成出现错误: {e}")

def custom_api_key_example():
    """自定义 API 密钥示例"""
    print("\n📋 自定义 API 密钥示例")
    print("=" * 30)
    
    try:
        # 使用自定义 API 密钥创建生成器
        custom_api_key = Config.get_api_key()  # 实际使用时替换为您的密钥
        generator = GeminiImageGenerator(api_key=custom_api_key)
        
        # 生成图像
        result = generator.generate_image("传统中国书法艺术")
        
        if result['success']:
            print(f"✅ 使用自定义 API 密钥成功生成 {result['image_count']} 张图片")
        else:
            print(f"❌ 生成失败: {result['error']}")
            
    except Exception as e:
        print(f"❌ 自定义 API 密钥示例出现错误: {e}")

def error_handling_example():
    """错误处理示例"""
    print("\n📋 错误处理示例")
    print("=" * 30)
    
    try:
        # 使用无效的 API 密钥测试错误处理
        generator = GeminiImageGenerator(api_key="invalid_key_for_testing")
        
        result = generator.generate_image("测试图像")
        
        if not result['success']:
            print(f"✅ 错误处理正常: {result['error']}")
        else:
            print("⚠️ 预期应该失败，但成功了")
            
    except Exception as e:
        print(f"✅ 捕获到预期的异常: {e}")

def main():
    """主函数"""
    print("🎨 GeminiImageGenerator 类使用示例")
    print("=" * 50)
    
    # 运行各种示例
    basic_usage_example()
    batch_generation_example()
    custom_api_key_example()
    error_handling_example()
    
    print("\n🎉 所有示例运行完成！")

if __name__ == "__main__":
    main()
