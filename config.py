"""
配置文件
用于管理Gemini API设置和其他配置项
"""

import os
from pathlib import Path

class Config:
    """配置类"""
    
    # API配置
    GEMINI_API_KEY = os.getenv('GEMINI_API_KEY', '')
    
    # 模型配置
    DEFAULT_MODEL = "gemini-2.0-flash-preview-image-generation"
    
    # 输出配置
    DEFAULT_OUTPUT_DIR = "generated_images"
    DEFAULT_IMAGE_FORMAT = "jpg"
    
    # 文件配置
    MAX_FILENAME_LENGTH = 100
    
    # 日志配置
    LOG_LEVEL = "INFO"
    LOG_FORMAT = '%(asctime)s - %(levelname)s - %(message)s'
    
    # 支持的图像格式
    SUPPORTED_MIME_TYPES = {
        'image/jpeg': '.jpg',
        'image/png': '.png',
        'image/gif': '.gif',
        'image/webp': '.webp',
        'image/bmp': '.bmp'
    }
    
    @classmethod
    def validate_api_key(cls) -> bool:
        """验证API密钥是否存在"""
        return bool(cls.GEMINI_API_KEY)
    
    @classmethod
    def get_output_path(cls, custom_dir: str = None) -> Path:
        """获取输出路径"""
        output_dir = custom_dir or cls.DEFAULT_OUTPUT_DIR
        path = Path(output_dir)
        path.mkdir(exist_ok=True)
        return path
