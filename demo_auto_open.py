"""
演示自动打开图片功能
"""

import time
from dotenv import load_dotenv
from image_gen import GeminiImageGenerator

# 加载环境变量
load_dotenv()

def demo_auto_open_feature():
    """演示自动打开功能的不同用法"""
    
    print("🎨 Gemini 图像生成器 - 自动打开功能演示")
    print("=" * 50)
    
    try:
        # 创建生成器
        generator = GeminiImageGenerator()
        
        # 演示1: 生成图像并自动打开
        print("\n📸 演示1: 生成图像并自动打开")
        print("正在生成: 一朵向日葵...")
        
        result1 = generator.generate_image(
            prompt="一朵明亮的向日葵，黄色花瓣，绿色茎叶，阳光明媚的背景",
            filename="sunflower_demo",
            auto_open=True,  # 自动打开
            save_text_response=False
        )
        
        print(f"✅ 生成完成！图片已自动打开")
        print(f"📁 文件位置: {result1['files_saved'][0]}")
        
        # 等待一下
        print("\n⏳ 等待3秒...")
        time.sleep(3)
        
        # 演示2: 生成图像但不自动打开
        print("\n📸 演示2: 生成图像但不自动打开")
        print("正在生成: 一只小鸟...")
        
        result2 = generator.generate_image(
            prompt="一只可爱的小鸟站在树枝上，蓝色羽毛，卡通风格",
            filename="bird_demo",
            auto_open=False,  # 不自动打开
            save_text_response=False
        )
        
        print(f"✅ 生成完成！图片已保存但未自动打开")
        print(f"📁 文件位置: {result2['files_saved'][0]}")
        
        # 演示3: 批量生成，只打开最后一张
        print("\n📸 演示3: 批量生成示例")
        
        prompts = [
            ("星空", "美丽的星空，银河系，深蓝色夜空"),
            ("海浪", "海浪拍打岩石，蓝色海水，白色泡沫"),
            ("彩虹", "雨后彩虹，七种颜色，天空背景")
        ]
        
        for i, (name, prompt) in enumerate(prompts, 1):
            print(f"正在生成第{i}张: {name}...")
            
            # 只有最后一张自动打开
            auto_open = (i == len(prompts))
            
            result = generator.generate_image(
                prompt=prompt,
                filename=f"batch_demo_{i}_{name}",
                auto_open=auto_open,
                save_text_response=False
            )
            
            if auto_open:
                print(f"✅ 第{i}张生成完成并自动打开！")
            else:
                print(f"✅ 第{i}张生成完成！")
        
        print("\n🎉 所有演示完成！")
        print("💡 提示: 您可以在代码中设置 auto_open=True/False 来控制是否自动打开图片")
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")

def show_usage_examples():
    """显示使用示例"""
    print("\n📖 使用示例:")
    print("-" * 30)
    
    examples = [
        ("自动打开（默认）", "generator.generate_image(prompt='...', auto_open=True)"),
        ("不自动打开", "generator.generate_image(prompt='...', auto_open=False)"),
        ("使用默认设置", "generator.generate_image(prompt='...')  # 默认会自动打开")
    ]
    
    for title, code in examples:
        print(f"\n{title}:")
        print(f"  {code}")

if __name__ == "__main__":
    demo_auto_open_feature()
    show_usage_examples()
