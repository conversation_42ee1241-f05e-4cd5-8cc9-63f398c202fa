"""
依赖检查模块
用于检查和验证所需的Python包是否已安装
"""

import sys
import importlib.util

def check_dependencies():
    """检查所需的依赖包"""
    missing_packages = []
    
    print("🔍 检查依赖包...")
    
    # 检查 google-genai
    try:
        from google import genai
        from google.genai import types
        print("✅ google-genai 已安装")
    except ImportError:
        missing_packages.append("google-genai")
        print("❌ google-genai 未安装")
    
    # 检查 pillow
    try:
        from PIL import Image
        print("✅ pillow 已安装")
    except ImportError:
        missing_packages.append("pillow")
        print("❌ pillow 未安装")
    
    # 检查 python-dotenv
    try:
        import dotenv
        print("✅ python-dotenv 已安装")
    except ImportError:
        missing_packages.append("python-dotenv")
        print("❌ python-dotenv 未安装")
    
    # 检查 httpx (可选，但推荐)
    try:
        import httpx
        print("✅ httpx 已安装")
    except ImportError:
        print("⚠️ httpx 未安装 (可选包)")
    
    if missing_packages:
        print(f"\n❌ 发现缺失的包:")
        for package in missing_packages:
            print(f"   - {package}")
        print(f"\n💡 请运行以下命令安装缺失的包:")
        print(f"   pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ 所有必需的依赖包都已安装")
    return True

def check_package_version(package_name, min_version=None):
    """
    检查特定包的版本
    
    Args:
        package_name: 包名
        min_version: 最小版本要求 (可选)
    
    Returns:
        tuple: (是否安装, 版本号)
    """
    try:
        spec = importlib.util.find_spec(package_name)
        if spec is None:
            return False, None
        
        module = importlib.import_module(package_name)
        version = getattr(module, '__version__', 'unknown')
        
        if min_version and version != 'unknown':
            # 简单的版本比较 (仅适用于数字版本)
            try:
                current_parts = [int(x) for x in version.split('.')]
                min_parts = [int(x) for x in min_version.split('.')]
                
                if current_parts < min_parts:
                    return True, f"{version} (需要 >= {min_version})"
            except ValueError:
                pass  # 版本格式不标准，跳过比较
        
        return True, version
    except ImportError:
        return False, None

def detailed_dependency_check():
    """详细的依赖检查，包含版本信息"""
    print("🔍 详细依赖检查...")
    print("=" * 50)
    
    dependencies = [
        ("google.genai", "google-genai"),
        ("PIL", "pillow"),
        ("dotenv", "python-dotenv"),
        ("httpx", "httpx")
    ]
    
    all_good = True
    
    for import_name, package_name in dependencies:
        installed, version = check_package_version(import_name)
        
        if installed:
            print(f"✅ {package_name}: {version}")
        else:
            print(f"❌ {package_name}: 未安装")
            all_good = False
    
    print("=" * 50)
    
    if all_good:
        print("🎉 所有依赖都已正确安装！")
    else:
        print("⚠️ 请安装缺失的依赖包")
    
    return all_good

def install_missing_dependencies():
    """尝试自动安装缺失的依赖"""
    import subprocess
    
    print("🔧 尝试自动安装缺失的依赖...")
    
    required_packages = [
        "google-genai",
        "pillow", 
        "python-dotenv"
    ]
    
    for package in required_packages:
        try:
            print(f"📦 安装 {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print(f"✅ {package} 安装成功")
        except subprocess.CalledProcessError as e:
            print(f"❌ {package} 安装失败: {e}")
            return False
    
    print("🎉 所有依赖安装完成！")
    return True

if __name__ == "__main__":
    """直接运行此文件时执行依赖检查"""
    print("🔍 独立依赖检查工具")
    print("=" * 30)
    
    # 执行详细检查
    if detailed_dependency_check():
        print("\n✅ 依赖检查通过，可以正常使用程序")
    else:
        print("\n❌ 依赖检查失败")
        
        # 询问是否自动安装
        try:
            response = input("\n是否尝试自动安装缺失的依赖? (y/n): ").lower().strip()
            if response in ['y', 'yes', '是']:
                install_missing_dependencies()
                print("\n重新检查依赖...")
                detailed_dependency_check()
        except KeyboardInterrupt:
            print("\n\n👋 用户取消操作")
        except Exception as e:
            print(f"\n❌ 操作失败: {e}")
