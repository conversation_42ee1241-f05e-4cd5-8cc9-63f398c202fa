"""
Gemini图像生成使用示例
"""

import os
from dotenv import load_dotenv
from image_gen import GeminiImageGenerator

# 加载环境变量
load_dotenv()

def example_basic_usage():
    """基本使用示例"""
    print("=== 基本使用示例 ===")
    
    try:
        # 创建生成器
        generator = GeminiImageGenerator()
        
        # 生成图像
        result = generator.generate_image(
            prompt="一只可爱的小猫在花园里玩耍，卡通风格，色彩鲜艳",
            filename="cute_cat"
        )
        
        print(f"生成成功！保存的文件: {result['files_saved']}")
        
    except Exception as e:
        print(f"生成失败: {e}")

def example_custom_settings():
    """自定义设置示例"""
    print("\n=== 自定义设置示例 ===")
    
    try:
        # 使用自定义API密钥创建生成器
        api_key = os.getenv('GEMINI_API_KEY')
        generator = GeminiImageGenerator(api_key=api_key)
        
        # 生成多个图像
        prompts = [
            "山水画，中国传统风格，高山流水",
            "现代城市夜景，霓虹灯闪烁，赛博朋克风格",
            "森林中的小木屋，温馨的灯光，童话风格"
        ]
        
        for i, prompt in enumerate(prompts, 1):
            result = generator.generate_image(
                prompt=prompt,
                output_dir="custom_images",
                filename=f"image_{i:02d}",
                save_text_response=True
            )
            print(f"图像 {i} 生成完成: {len(result['files_saved'])} 个文件")
            
    except Exception as e:
        print(f"生成失败: {e}")

def example_batch_generation():
    """批量生成示例"""
    print("\n=== 批量生成示例 ===")
    
    # 中国传统文化主题的提示词
    traditional_prompts = {
        "dragon": "中国龙，金色鳞片，在云雾中飞舞，传统中国画风格",
        "phoenix": "凤凰，五彩羽毛，展翅高飞，工笔画风格",
        "landscape": "桂林山水，漓江风光，水墨画风格，意境深远",
        "bamboo": "竹林，清风徐来，水墨画，简约雅致",
        "plum_blossom": "梅花，雪中绽放，国画风格，寓意坚韧"
    }
    
    try:
        generator = GeminiImageGenerator()
        
        for name, prompt in traditional_prompts.items():
            print(f"正在生成: {name}")
            result = generator.generate_image(
                prompt=prompt,
                output_dir="traditional_art",
                filename=f"traditional_{name}"
            )
            print(f"  完成: {len(result['files_saved'])} 个文件")
            
    except Exception as e:
        print(f"批量生成失败: {e}")

if __name__ == "__main__":
    # 检查API密钥
    if not os.getenv('GEMINI_API_KEY'):
        print("错误: 请设置GEMINI_API_KEY环境变量")
        print("您可以:")
        print("1. 创建.env文件并设置GEMINI_API_KEY")
        print("2. 或者在系统环境变量中设置GEMINI_API_KEY")
        exit(1)
    
    # 运行示例
    example_basic_usage()
    example_custom_settings()
    example_batch_generation()
    
    print("\n所有示例执行完成！")
