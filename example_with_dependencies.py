"""
示例：如何在其他文件中使用依赖检查模块
"""

from dependencies import check_dependencies, detailed_dependency_check

def main():
    """主函数示例"""
    print("📋 示例程序 - 使用独立的依赖检查")
    print("=" * 40)
    
    # 方法1: 简单检查
    print("\n🔍 方法1: 简单依赖检查")
    if check_dependencies():
        print("✅ 依赖检查通过，可以继续执行程序")
        
        # 这里可以放置您的主要程序逻辑
        print("🚀 程序主要逻辑开始执行...")
        print("   - 初始化组件...")
        print("   - 加载配置...")
        print("   - 执行任务...")
        print("✅ 程序执行完成")
        
    else:
        print("❌ 依赖检查失败，程序无法继续")
        return
    
    # 方法2: 详细检查
    print("\n🔍 方法2: 详细依赖检查")
    detailed_dependency_check()

if __name__ == "__main__":
    main()
