import os
import base64
import logging
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any
from dotenv import load_dotenv

from google import genai
from google.genai import types
from PIL import Image

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class GeminiImageGenerator:
    """Gemini图像生成器类"""

    def __init__(self, api_key: Optional[str] = None):
        """
        初始化Gemini图像生成器

        Args:
            api_key: Gemini API密钥，如果不提供则从环境变量GEMINI_API_KEY获取
        """
        self.api_key = api_key or os.getenv('GEMINI_API_KEY')
        if not self.api_key:
            raise ValueError("API密钥未提供。请设置环境变量GEMINI_API_KEY或直接传入api_key参数")

        try:
            self.client = genai.Client(api_key=self.api_key)
            logger.info("Gemini客户端初始化成功")
        except Exception as e:
            logger.error(f"Gemini客户端初始化失败: {e}")
            raise

    def generate_image(self,
                      prompt: str,
                      output_dir: str = "generated_images",
                      filename: Optional[str] = None,
                      save_text_response: bool = True) -> Dict[str, Any]:
        """
        生成图像

        Args:
            prompt: 图像生成提示词
            output_dir: 输出目录
            filename: 文件名（不包含扩展名），如果不提供则使用时间戳
            save_text_response: 是否保存文本响应

        Returns:
            包含生成结果信息的字典
        """
        try:
            logger.info(f"开始生成图像，提示词: {prompt}")

            # 调用Gemini API生成内容
            response = self.client.models.generate_content(
                model="gemini-2.0-flash-preview-image-generation",
                contents=prompt,
                config=types.GenerateContentConfig(response_modalities=['Text', 'Image'])
            )

            # 创建输出目录
            output_path = Path(output_dir)
            output_path.mkdir(exist_ok=True)

            # 生成文件名
            if not filename:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"gemini_image_{timestamp}"

            result = {
                "prompt": prompt,
                "timestamp": datetime.now().isoformat(),
                "files_saved": [],
                "text_response": None
            }

            # 处理响应
            if hasattr(response, 'candidates') and response.candidates:
                candidate = response.candidates[0]

                # 保存文本响应
                if hasattr(candidate, 'content') and candidate.content.parts:
                    for part in candidate.content.parts:
                        if hasattr(part, 'text') and part.text:
                            result["text_response"] = part.text
                            if save_text_response:
                                text_file = output_path / f"{filename}_description.txt"
                                with open(text_file, 'w', encoding='utf-8') as f:
                                    f.write(f"提示词: {prompt}\n\n")
                                    f.write(f"生成时间: {result['timestamp']}\n\n")
                                    f.write(f"描述: {part.text}")
                                result["files_saved"].append(str(text_file))
                                logger.info(f"文本描述已保存到: {text_file}")

                        # 保存图像
                        elif hasattr(part, 'inline_data'):
                            image_data = part.inline_data.data
                            mime_type = part.inline_data.mime_type

                            # 根据MIME类型确定文件扩展名
                            extension = self._get_extension_from_mime(mime_type)
                            image_file = output_path / f"{filename}{extension}"

                            # 解码并保存图像
                            image_bytes = base64.b64decode(image_data)
                            with open(image_file, 'wb') as f:
                                f.write(image_bytes)

                            result["files_saved"].append(str(image_file))
                            logger.info(f"图像已保存到: {image_file}")

            if not result["files_saved"]:
                logger.warning("未生成任何文件")

            return result

        except Exception as e:
            logger.error(f"图像生成失败: {e}")
            raise

    def _get_extension_from_mime(self, mime_type: str) -> str:
        """根据MIME类型获取文件扩展名"""
        mime_to_ext = {
            'image/jpeg': '.jpg',
            'image/png': '.png',
            'image/gif': '.gif',
            'image/webp': '.webp',
            'image/bmp': '.bmp'
        }
        return mime_to_ext.get(mime_type, '.jpg')

def main():
    """主函数示例"""
    try:
        # 创建生成器实例
        generator = GeminiImageGenerator()

        # 图片生成提示词
        prompt = "八骏图，中国传统绘画风格，八匹骏马在草原上奔跑，水墨画风格，高质量，艺术感"

        # 生成图像
        result = generator.generate_image(
            prompt=prompt,
            output_dir="generated_images",
            filename="bajuntu"
        )

        print("=" * 50)
        print("图像生成完成！")
        print(f"提示词: {result['prompt']}")
        print(f"生成时间: {result['timestamp']}")
        if result['text_response']:
            print(f"AI描述: {result['text_response']}")
        print("保存的文件:")
        for file_path in result['files_saved']:
            print(f"  - {file_path}")
        print("=" * 50)

    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        print(f"错误: {e}")

if __name__ == "__main__":
    main()