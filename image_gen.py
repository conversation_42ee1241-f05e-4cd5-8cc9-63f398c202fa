import os
import sys
from io import BytesIO
import base64
from dotenv import load_dotenv # .env

load_dotenv()
# 命令行中 
# set HTTP_PROXY=http://10.158.100.3:8080
# set HTTPS_PROXY=http://10.158.100.3:8080


def check_dependencies():
    """检查所需的依赖包"""
    missing_packages = []

    try:
        from google import genai
        from google.genai import types
        print("✅ google-genai 已安装")
    except ImportError:
        missing_packages.append("google-genai")
        print("❌ google-genai 未安装")

    try:
        from PIL import Image
        print("✅ pillow 已安装")
    except ImportError:
        missing_packages.append("pillow")
        print("❌ pillow 未安装")

    if missing_packages:
        print(f"\n请安装缺失的包: pip install {' '.join(missing_packages)}")
        return False

    return True

def main():
    """主函数"""
    print("🖼️ Google Gemini 图片生成程序")
    print("=" * 50)

    # 检查依赖
    if not check_dependencies():
        print("❌ 依赖检查失败，程序退出")
        return

    # 导入所需模块
    from google import genai
    from google.genai import types
    from PIL import Image
    import httpx
    import os

    try:
        # 代理配置 - 检查环境变量
        http_proxy = os.getenv("HTTP_PROXY")
        https_proxy = os.getenv("HTTPS_PROXY")
        proxy_url = os.getenv("PROXY_URL")

        # 使用您提供的API密钥创建客户端
        print("🔗 连接到 Google Gemini API...")

        # 显示代理状态
        if http_proxy or https_proxy or proxy_url:
            print("🌐 检测到代理设置:")
            if http_proxy:
                print(f"   HTTP_PROXY: {http_proxy}")
            if https_proxy:
                print(f"   HTTPS_PROXY: {https_proxy}")
            if proxy_url:
                print(f"   PROXY_URL: {proxy_url}")
        else:
            print("🔗 直连模式 (未检测到代理设置)")

        # Google GenAI 会自动使用系统的 HTTP_PROXY 和 HTTPS_PROXY 环境变量
        client = genai.Client(api_key="AIzaSyDw3Z1JLckTkAc47hxVb8PuDiIr80zSbbs")

        # 图片生成提示词
        contents = ('八骏图')

        print("🤖 发送图片生成请求...")
        print(f"📝 提示词: {contents}")

        # 尝试生成图片 - 使用 Gemini 2.0 Flash Experimental 模型
        try:
            print("🔄 尝试使用 gemini-2.0-flash-preview-image-generation 模型...")
            response = client.models.generate_content(
                model="gemini-2.0-flash-preview-image-generation",
                contents=contents,
                config=types.GenerateContentConfig(response_modalities=['Text', 'Image'])
            )
        except Exception as img_error:
            error_msg = str(img_error).lower()
            if "not available in your country" in error_msg:
                print("⚠️ 图片生成功能在当前地区不可用，改为生成详细描述...")
            elif "timeout" in error_msg or "connect" in error_msg:
                print("⚠️ 网络连接超时，尝试使用备用模型...")
            else:
                print(f"⚠️ 模型调用失败: {str(img_error)[:100]}...")

            print("🔄 改用 gemini-2.5-flash 生成文本描述...")
            # 改为只生成文本描述
            response = client.models.generate_content(
                model="gemini-2.5-flash",
                contents=f"请为以下场景创建一个非常详细的视觉描述，包括颜色、光线、构图等细节：{contents}"
            )

        print("✅ 收到响应，处理结果...")

        # 检查响应是否有效
        if not response or not response.candidates:
            print("❌ 未收到有效响应")
            return

        candidate = response.candidates[0]
        if not candidate or not candidate.content or not candidate.content.parts:
            print("❌ 响应中没有内容")
            return

        # 处理响应内容
        image_count = 0
        for i, part in enumerate(candidate.content.parts):
            if part.text is not None:
                print(f"📝 AI 回复: {part.text}")
            elif part.inline_data is not None and part.inline_data.data is not None:
                image_count += 1
                print(f"🖼️ 处理生成的图片 {image_count}...")

                try:
                    # 获取图片数据
                    image_data = part.inline_data.data
                    if isinstance(image_data, bytes):
                        # 打开并保存图片
                        image = Image.open(BytesIO(image_data))
                        output_filename = f'gemini-native-image-{image_count}.png'
                        image.save(output_filename)
                        print(f"💾 图片已保存为: {output_filename}")

                        # 尝试显示图片
                        try:
                            image.show()
                            print("🖼️ 图片已在默认查看器中打开")
                        except Exception as e:
                            print(f"⚠️ 无法自动打开图片: {e}")
                            print("您可以手动打开文件查看图片")
                    else:
                        print(f"⚠️ 图片数据格式不正确: {type(image_data)}")

                except Exception as e:
                    print(f"❌ 处理图片时出错: {e}")

        if image_count == 0:
            print("⚠️ 响应中没有找到图片数据")
        else:
            print(f"🎉 成功生成并保存了 {image_count} 张图片！")

    except Exception as e:
        print(f"❌ 程序运行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()