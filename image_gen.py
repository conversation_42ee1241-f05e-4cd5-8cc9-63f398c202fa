"""
Google Gemini 图像生成程序
支持通过 Gemini API 生成高质量图像
"""

import os
import sys
import platform
import subprocess
from io import BytesIO
import base64
from dotenv import load_dotenv

# Google Gemini API 相关导入
try:
    from google import genai
    from google.genai import types
except ImportError:
    # 如果导入失败，在运行时会被依赖检查捕获
    genai = None
    types = None

# 图像处理相关导入
try:
    from PIL import Image
except ImportError:
    Image = None

# HTTP 客户端 (可选)
try:
    import httpx
except ImportError:
    httpx = None

# 加载环境变量
load_dotenv()

# 从独立模块导入依赖检查函数
from dependencies import check_dependencies

# 命令行中设置代理的方法：
# set HTTP_PROXY=http://10.158.100.3:8080
# set HTTPS_PROXY=http://10.158.100.3:8080


class Config:
    """配置管理类"""

    # API 配置
    DEFAULT_API_KEY = "AIzaSyDw3Z1JLckTkAc47hxVb8PuDiIr80zSbbs"
    DEFAULT_MODEL = "gemini-2.0-flash-preview-image-generation"
    FALLBACK_MODEL = "gemini-2.5-flash"

    # 图片配置
    DEFAULT_PROMPT = "八骏图"
    OUTPUT_PREFIX = "gemini-native-image"
    IMAGE_FORMAT = "png"

    @staticmethod
    def get_api_key():
        """获取 API 密钥"""
        return os.getenv("GEMINI_API_KEY", Config.DEFAULT_API_KEY)

    @staticmethod
    def get_proxy_info():
        """获取代理信息"""
        return {
            'http_proxy': os.getenv("HTTP_PROXY"),
            'https_proxy': os.getenv("HTTPS_PROXY"),
            'proxy_url': os.getenv("PROXY_URL")
        }

    @staticmethod
    def has_proxy():
        """检查是否配置了代理"""
        proxy_info = Config.get_proxy_info()
        return any(proxy_info.values())

def main():
    """主函数"""
    print("🖼️ Google Gemini 图片生成程序")
    print("=" * 50)

    # 检查依赖
    if not check_dependencies():
        print("❌ 依赖检查失败，程序退出")
        return

    # 所需模块已在文件顶部导入

    try:
        # 检查 genai 模块是否可用
        if genai is None:
            print("❌ google-genai 模块未正确导入")
            return

        # 使用配置类管理设置
        print("🔗 连接到 Google Gemini API...")

        # 显示代理状态
        if Config.has_proxy():
            print("🌐 检测到代理设置:")
            proxy_info = Config.get_proxy_info()
            for key, value in proxy_info.items():
                if value:
                    print(f"   {key.upper()}: {value}")
        else:
            print("🔗 直连模式 (未检测到代理设置)")

        # Google GenAI 会自动使用系统的 HTTP_PROXY 和 HTTPS_PROXY 环境变量
        client = genai.Client(api_key=Config.get_api_key())

        # 图片生成提示词
        contents = Config.DEFAULT_PROMPT

        print("🤖 发送图片生成请求...")
        print(f"📝 提示词: {contents}")

        # 尝试生成图片 - 使用 Gemini 2.0 Flash 图像生成模型
        try:
            print(f"🔄 尝试使用 {Config.DEFAULT_MODEL} 模型...")

            # 检查 types 模块是否可用
            if types is None:
                print("❌ google.genai.types 模块未正确导入")
                return

            response = client.models.generate_content(
                model=Config.DEFAULT_MODEL,
                contents=contents,
                config=types.GenerateContentConfig(response_modalities=['Text', 'Image'])
            )
        except Exception as img_error:
            error_msg = str(img_error).lower()
            if "not available in your country" in error_msg:
                print("⚠️ 图片生成功能在当前地区不可用，改为生成详细描述...")
            elif "timeout" in error_msg or "connect" in error_msg:
                print("⚠️ 网络连接超时，尝试使用备用模型...")
            else:
                print(f"⚠️ 模型调用失败: {str(img_error)[:100]}...")

            print(f"🔄 改用 {Config.FALLBACK_MODEL} 生成文本描述...")
            # 改为只生成文本描述
            response = client.models.generate_content(
                model=Config.FALLBACK_MODEL,
                contents=f"请为以下场景创建一个非常详细的视觉描述，包括颜色、光线、构图等细节：{contents}"
            )

        print("✅ 收到响应，处理结果...")

        # 检查响应是否有效
        if not response or not response.candidates:
            print("❌ 未收到有效响应")
            return

        candidate = response.candidates[0]
        if not candidate or not candidate.content or not candidate.content.parts:
            print("❌ 响应中没有内容")
            return

        # 处理响应内容
        image_count = 0
        for part in candidate.content.parts:
            if part.text is not None:
                print(f"📝 AI 回复: {part.text}")
            elif part.inline_data is not None and part.inline_data.data is not None:
                image_count += 1
                print(f"🖼️ 处理生成的图片 {image_count}...")

                try:
                    # 检查 PIL 模块是否可用
                    if Image is None:
                        print("❌ PIL (Pillow) 模块未正确导入，无法处理图片")
                        continue

                    # 获取图片数据
                    image_data = part.inline_data.data
                    if isinstance(image_data, bytes):
                        # 打开并保存图片
                        image = Image.open(BytesIO(image_data))
                        output_filename = f'gemini-native-image-{image_count}.png'
                        image.save(output_filename)
                        print(f"💾 图片已保存为: {output_filename}")

                        # 尝试显示图片
                        try:
                            image.show()
                            print("🖼️ 图片已在默认查看器中打开")
                        except Exception as e:
                            print(f"⚠️ 无法自动打开图片: {e}")
                            print("您可以手动打开文件查看图片")
                    else:
                        print(f"⚠️ 图片数据格式不正确: {type(image_data)}")

                except Exception as e:
                    print(f"❌ 处理图片时出错: {e}")

        if image_count == 0:
            print("⚠️ 响应中没有找到图片数据")
        else:
            print(f"🎉 成功生成并保存了 {image_count} 张图片！")

    except Exception as e:
        print(f"❌ 程序运行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()