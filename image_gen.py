"""
Google Gemini 图像生成程序
支持通过 Gemini API 生成高质量图像
"""

import os
import sys
import platform
import subprocess
from io import BytesIO
import base64
from dotenv import load_dotenv

# Google Gemini API 相关导入
try:
    from google import genai
    from google.genai import types
except ImportError:
    # 如果导入失败，在运行时会被依赖检查捕获
    genai = None
    types = None

# 图像处理相关导入
try:
    from PIL import Image
except ImportError:
    Image = None

# HTTP 客户端 (可选)
try:
    import httpx
except ImportError:
    httpx = None

# 加载环境变量
load_dotenv()

# 从独立模块导入依赖检查函数
from dependencies import check_dependencies

# 命令行中设置代理的方法：
# set HTTP_PROXY=http://10.158.100.3:8080
# set HTTPS_PROXY=http://10.158.100.3:8080


class Config:
    """配置管理类"""

    # API 配置
    DEFAULT_API_KEY = "AIzaSyDw3Z1JLckTkAc47hxVb8PuDiIr80zSbbs"
    DEFAULT_MODEL = "gemini-2.0-flash-preview-image-generation"
    FALLBACK_MODEL = "gemini-2.5-flash"

    # 图片配置
    DEFAULT_PROMPT = "八骏图"
    OUTPUT_PREFIX = "gemini-native-image"
    IMAGE_FORMAT = "png"

    @staticmethod
    def get_api_key():
        """获取 API 密钥"""
        return os.getenv("GEMINI_API_KEY", Config.DEFAULT_API_KEY)

    @staticmethod
    def get_proxy_info():
        """获取代理信息"""
        return {
            'http_proxy': os.getenv("HTTP_PROXY"),
            'https_proxy': os.getenv("HTTPS_PROXY"),
            'proxy_url': os.getenv("PROXY_URL")
        }

    @staticmethod
    def has_proxy():
        """检查是否配置了代理"""
        proxy_info = Config.get_proxy_info()
        return any(proxy_info.values())


class GeminiImageGenerator:
    """Google Gemini 图像生成器类"""

    def __init__(self, api_key=None):
        """
        初始化图像生成器

        Args:
            api_key: API 密钥，如果不提供则使用配置中的默认值
        """
        self.api_key = api_key or Config.get_api_key()
        self.client = None
        self._initialize_client()

    def _initialize_client(self):
        """初始化 Gemini 客户端"""
        if genai is None:
            raise ImportError("google-genai 模块未正确导入")

        try:
            self.client = genai.Client(api_key=self.api_key)
            print("✅ Gemini 客户端初始化成功")
        except Exception as e:
            raise RuntimeError(f"无法初始化 Gemini 客户端: {e}")

    def _show_proxy_status(self):
        """显示代理状态"""
        if Config.has_proxy():
            print("🌐 检测到代理设置:")
            proxy_info = Config.get_proxy_info()
            for key, value in proxy_info.items():
                if value:
                    print(f"   {key.upper()}: {value}")
        else:
            print("🔗 直连模式 (未检测到代理设置)")

    def _save_image(self, image_data, image_count):
        """
        保存图像数据到文件

        Args:
            image_data: 图像二进制数据
            image_count: 图像编号

        Returns:
            str: 保存的文件名，如果失败返回 None
        """
        if Image is None:
            print("❌ PIL (Pillow) 模块未正确导入，无法处理图片")
            return None

        try:
            if isinstance(image_data, bytes):
                # 打开并保存图片
                image = Image.open(BytesIO(image_data))
                output_filename = f'{Config.OUTPUT_PREFIX}-{image_count}.{Config.IMAGE_FORMAT}'
                image.save(output_filename)
                print(f"💾 图片已保存为: {output_filename}")

                # 尝试显示图片
                try:
                    image.show()
                    print("🖼️ 图片已在默认查看器中打开")
                except Exception as e:
                    print(f"⚠️ 无法自动打开图片: {e}")
                    print("您可以手动打开文件查看图片")

                return output_filename
            else:
                print(f"⚠️ 图片数据格式不正确: {type(image_data)}")
                return None

        except Exception as e:
            print(f"❌ 处理图片时出错: {e}")
            return None

    def generate_image(self, prompt=None, show_proxy_status=True):
        """
        生成图像

        Args:
            prompt: 图像生成提示词，如果不提供则使用默认值
            show_proxy_status: 是否显示代理状态

        Returns:
            dict: 生成结果，包含成功状态、图片文件列表、AI回复等信息
        """
        if self.client is None:
            return {
                'success': False,
                'error': 'Gemini 客户端未初始化',
                'images': [],
                'ai_response': None
            }

        prompt = prompt or Config.DEFAULT_PROMPT

        print("🤖 发送图片生成请求...")
        print(f"📝 提示词: {prompt}")

        if show_proxy_status:
            self._show_proxy_status()

        try:
            # 尝试生成图片
            print(f"🔄 尝试使用 {Config.DEFAULT_MODEL} 模型...")

            if types is None:
                raise ImportError("google.genai.types 模块未正确导入")

            response = self.client.models.generate_content(
                model=Config.DEFAULT_MODEL,
                contents=prompt,
                config=types.GenerateContentConfig(response_modalities=['Text', 'Image'])
            )

        except Exception as img_error:
            error_msg = str(img_error).lower()
            if "not available in your country" in error_msg:
                print("⚠️ 图片生成功能在当前地区不可用，改为生成详细描述...")
            elif "timeout" in error_msg or "connect" in error_msg:
                print("⚠️ 网络连接超时，尝试使用备用模型...")
            else:
                print(f"⚠️ 模型调用失败: {str(img_error)[:100]}...")

            print(f"🔄 改用 {Config.FALLBACK_MODEL} 生成文本描述...")
            try:
                response = self.client.models.generate_content(
                    model=Config.FALLBACK_MODEL,
                    contents=f"请为以下场景创建一个非常详细的视觉描述，包括颜色、光线、构图等细节：{prompt}"
                )
            except Exception as fallback_error:
                return {
                    'success': False,
                    'error': f'所有模型调用都失败: {fallback_error}',
                    'images': [],
                    'ai_response': None
                }

        return self._process_response(response)

    def _process_response(self, response):
        """
        处理 API 响应

        Args:
            response: Gemini API 响应

        Returns:
            dict: 处理结果
        """
        print("✅ 收到响应，处理结果...")

        # 检查响应是否有效
        if not response or not response.candidates:
            return {
                'success': False,
                'error': '未收到有效响应',
                'images': [],
                'ai_response': None
            }

        candidate = response.candidates[0]
        if not candidate or not candidate.content or not candidate.content.parts:
            return {
                'success': False,
                'error': '响应中没有内容',
                'images': [],
                'ai_response': None
            }

        # 处理响应内容
        image_count = 0
        saved_images = []
        ai_response = None

        for part in candidate.content.parts:
            if part.text is not None:
                ai_response = part.text
                print(f"📝 AI 回复: {part.text}")
            elif part.inline_data is not None and part.inline_data.data is not None:
                image_count += 1
                print(f"🖼️ 处理生成的图片 {image_count}...")

                filename = self._save_image(part.inline_data.data, image_count)
                if filename:
                    saved_images.append(filename)

        if image_count == 0:
            print("⚠️ 响应中没有找到图片数据")
        else:
            print(f"🎉 成功生成并保存了 {image_count} 张图片！")

        return {
            'success': True,
            'error': None,
            'images': saved_images,
            'ai_response': ai_response,
            'image_count': image_count
        }

def main():
    """主函数"""
    print("🖼️ Google Gemini 图片生成程序")
    print("=" * 50)

    # 检查依赖
    if not check_dependencies():
        print("❌ 依赖检查失败，程序退出")
        return

    try:
        # 创建图像生成器实例
        print("🔗 初始化 Gemini 图像生成器...")
        generator = GeminiImageGenerator()

        # 生成图像
        result = generator.generate_image()

        # 处理结果
        if result['success']:
            if result['image_count'] > 0:
                print(f"\n✅ 任务完成！生成了 {result['image_count']} 张图片")
                print("� 生成的文件:")
                for img_file in result['images']:
                    print(f"   - {img_file}")
            else:
                print("\n✅ 任务完成！生成了文本描述")
        else:
            print(f"\n❌ 生成失败: {result['error']}")

    except Exception as e:
        print(f"❌ 程序运行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()