import os
import glob
from dotenv import load_dotenv
from google import genai
from google.genai import types
from image_gen import Config

# 加载环境变量
load_dotenv()

# 显示代理状态
if Config.has_proxy():
    print("🌐 检测到代理设置:")
    proxy_info = Config.get_proxy_info()
    for key, value in proxy_info.items():
        if value:
            print(f"   {key.upper()}: {value}")
else:
    print("🔗 直连模式 (未检测到代理设置)")

# 使用配置管理获取 API 密钥
print("🔗 连接到 Google Gemini API...")
client = genai.Client(api_key=Config.get_api_key())

# 从生成目录中查找最新的图片
output_dir = Config.get_output_dir()
image_pattern = os.path.join(output_dir, f"{Config.get_output_prefix()}-*.{Config.get_image_format()}")
image_files = glob.glob(image_pattern)

if not image_files:
    print(f"❌ 在 {output_dir} 目录中没有找到图片文件")
    print(f"请先运行 image_gen.py 生成图片")
    exit(1)

# 选择最新的图片文件
latest_image = max(image_files, key=os.path.getctime)
print(f"📖 正在分析图片: {latest_image}")

def analyze_image(image_path, prompt="请详细描述这张图片的内容，包括主要元素、颜色、风格和整体感觉。"):
    """分析图片内容"""
    try:
        print(f"🤖 开始分析图片...")

        with open(image_path, 'rb') as f:
            image_bytes = f.read()

        # 根据文件扩展名确定 MIME 类型
        file_ext = os.path.splitext(image_path)[1].lower()
        mime_type = 'image/png' if file_ext == '.png' else 'image/jpeg'

        response = client.models.generate_content(
            model='gemini-2.5-flash',
            contents=[
                types.Part.from_bytes(
                    data=image_bytes,
                    mime_type=mime_type,
                ),
                prompt
            ]
        )

        print("✅ 分析完成！")
        return response.text

    except Exception as e:
        print(f"❌ 分析图片时出错: {e}")
        return None

# 分析最新图片
result = analyze_image(latest_image)
if result:
    print("📝 图片描述:")
    print("-" * 50)
    print(result)

    # 可选：保存分析结果到文件
    save_result = input("\n💾 是否保存分析结果到文件？(y/n): ").lower().strip()
    if save_result == 'y':
        result_filename = latest_image.replace('.png', '_analysis.txt').replace('.jpg', '_analysis.txt')
        with open(result_filename, 'w', encoding='utf-8') as f:
            f.write(f"图片文件: {latest_image}\n")
            f.write(f"分析时间: {os.path.getctime(latest_image)}\n")
            f.write("-" * 50 + "\n")
            f.write(result)
        print(f"📄 分析结果已保存到: {result_filename}")
else:
    print("❌ 图片分析失败")