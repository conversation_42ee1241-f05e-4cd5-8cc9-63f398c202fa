#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主程序：完整的图片生成→分析→语音合成流程
"""

import os
import sys
import time
import glob
from dotenv import load_dotenv
from image_gen import GeminiImageGenerator, Config

# 加载环境变量
load_dotenv()

def print_banner():
    """打印程序横幅"""
    print("=" * 60)
    print("🎨 AI 图片生成与分析语音合成系统")
    print("=" * 60)
    print("📝 功能：图片生成 → 内容分析 → 语音合成")
    print("🔧 技术栈：Gemini API + 通义千问 TTS")
    print("=" * 60)

def get_user_prompt():
    """获取用户输入的提示词"""
    print("\n📝 请输入图片生成提示词：")
    prompt = input(">>> ").strip()
    
    if not prompt:
        print("❌ 提示词不能为空！")
        return get_user_prompt()
    
    return prompt

def step1_generate_image(prompt):
    """步骤1：生成图片"""
    print(f"\n🎨 步骤 1/3：生成图片")
    print(f"📝 提示词：{prompt}")
    print("-" * 40)
    
    try:
        generator = GeminiImageGenerator()
        generator.generate_image(prompt, show_proxy_status=True)
        print("✅ 图片生成完成！")
        return True
    except Exception as e:
        print(f"❌ 图片生成失败：{str(e)}")
        return False

def step2_analyze_image():
    """步骤2：分析图片"""
    print(f"\n🔍 步骤 2/3：分析图片内容")
    print("-" * 40)
    
    try:
        # 直接导入并调用图片分析函数
        from image_understand import main as analyze_main
        result = analyze_main()
        if result:
            print("✅ 图片分析完成！")
            return True
        else:
            print("❌ 图片分析失败")
            return False
    except Exception as e:
        print(f"❌ 图片分析失败：{str(e)}")
        return False

def step3_generate_speech():
    """步骤3：生成语音"""
    print(f"\n🔊 步骤 3/3：生成语音")
    print("-" * 40)
    
    try:
        # 直接导入并调用语音合成函数
        from speaker import main as speaker_main
        result = speaker_main()
        if result:
            print("✅ 语音合成完成！")
            return True
        else:
            print("❌ 语音合成失败")
            return False
    except Exception as e:
        print(f"❌ 语音合成失败：{str(e)}")
        return False

def show_results():
    """显示最终结果"""
    print(f"\n🎉 流程完成！生成的文件：")
    print("=" * 40)
    
    output_dir = Config.get_output_dir()
    
    # 查找最新生成的文件
    image_pattern = os.path.join(output_dir, f"{Config.get_output_prefix()}-*.png")
    image_files = glob.glob(image_pattern)
    
    if image_files:
        latest_image = max(image_files, key=os.path.getctime)
        basename = os.path.splitext(os.path.basename(latest_image))[0]
        
        # 显示所有相关文件
        files_to_check = [
            (f"{basename}.png", "🖼️ 生成的图片"),
            (f"{basename}_analysis.txt", "📄 分析结果"),
            (f"{basename}_audio.wav", "🔊 语音文件")
        ]
        
        for filename, description in files_to_check:
            filepath = os.path.join(output_dir, filename)
            if os.path.exists(filepath):
                file_size = os.path.getsize(filepath)
                print(f"{description}: {filepath} ({file_size:,} bytes)")
            else:
                print(f"{description}: ❌ 未找到")
    
    print("=" * 40)

def main():
    """主函数"""
    try:
        print_banner()
        
        # 获取用户输入
        prompt = get_user_prompt()
        
        print(f"\n🚀 开始执行完整流程...")
        start_time = time.time()
        
        # 步骤1：生成图片
        if not step1_generate_image(prompt):
            print("❌ 流程中断：图片生成失败")
            return
        
        # 步骤2：分析图片
        if not step2_analyze_image():
            print("❌ 流程中断：图片分析失败")
            return
        
        # 步骤3：生成语音
        if not step3_generate_speech():
            print("❌ 流程中断：语音合成失败")
            return
        
        # 显示结果
        show_results()
        
        # 计算总耗时
        total_time = time.time() - start_time
        print(f"\n⏱️ 总耗时：{total_time:.2f} 秒")
        print("🎉 所有步骤完成！")
        
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 程序执行出错：{str(e)}")

if __name__ == "__main__":
    main()
