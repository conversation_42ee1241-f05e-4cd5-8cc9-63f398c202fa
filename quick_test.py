"""
快速测试自动打开功能
"""

import os
from dotenv import load_dotenv
from image_gen import GeminiImageGenerator

# 加载环境变量
load_dotenv()

def main():
    print("开始测试...")
    
    # 检查API密钥
    api_key = os.getenv('GEMINI_API_KEY')
    if not api_key:
        print("错误: 未找到API密钥")
        return
    
    print("API密钥已找到")
    
    try:
        # 创建生成器
        generator = GeminiImageGenerator()
        print("生成器创建成功")
        
        # 生成一张简单的图像
        print("正在生成图像...")
        result = generator.generate_image(
            prompt="一个简单的红色圆圈",
            filename="test_circle",
            auto_open=True,
            save_text_response=False
        )
        
        print("生成完成！")
        print(f"文件: {result['files_saved']}")
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
