import os
import glob
import requests
import dashscope
from dotenv import load_dotenv
from image_gen import Config

def get_latest_analysis_file():
    """获取最新的图片分析文件路径"""
    output_dir = Config.get_output_dir()
    analysis_pattern = os.path.join(output_dir, f"{Config.get_output_prefix()}-*_analysis.txt")
    analysis_files = glob.glob(analysis_pattern)

    if not analysis_files:
        raise FileNotFoundError(f"在 {output_dir} 目录中未找到分析文件")

    # 选择最新的分析文件
    latest_analysis = max(analysis_files, key=os.path.getctime)
    print(f"📖 读取分析文件: {latest_analysis}")
    return latest_analysis

def extract_analysis_content(file_path):
    """从分析文件中提取内容（跳过文件头信息）"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()

    # 提取分析内容（跳过文件头信息）
    lines = content.split('\n')
    content_start = 0
    for i, line in enumerate(lines):
        if line.startswith('-' * 50):
            content_start = i + 1
            break

    return '\n'.join(lines[content_start:]).strip()

def main():
    """主函数"""
    from dotenv import load_dotenv

    # 加载环境变量
    load_dotenv()

    try:
        # 获取最新分析文件并读取内容
        latest_analysis_file = get_latest_analysis_file()
        full_text = extract_analysis_content(latest_analysis_file)
        print(f"📝 原始文本长度: {len(full_text)} 字符")

        # TTS API 限制输入长度为512字符，需要截取
        max_length = int(os.getenv("TTS_MAX_LENGTH", "500"))  # 留一些余量
        if len(full_text) > max_length:
            # 尝试在句号处截断，保持语义完整
            text = full_text[:max_length]
            last_period = text.rfind('。')
            if last_period > max_length // 2:  # 如果句号位置合理
                text = text[:last_period + 1]
            print(f"📝 文本已截取至: {len(text)} 字符")
        else:
            text = full_text
            print(f"📝 文本长度符合要求: {len(text)} 字符")

        # 调用语音合成API
        response = dashscope.audio.qwen_tts.SpeechSynthesizer.call(
            model=os.getenv("TTS_MODEL", "qwen-tts-latest"),
            api_key=os.getenv("DASHSCOPE_API_KEY"),
            text=text,
            voice=os.getenv("TTS_VOICE", "Sunny"),
        )

        print(response)

        audio_url = response.output.audio["url"]

        # 获取分析文件的基础名称，用于命名音频文件
        analysis_basename = os.path.splitext(os.path.basename(latest_analysis_file))[0]
        audio_basename = analysis_basename.replace('_analysis', '_audio')

        # 保存到同一目录
        output_dir = Config.get_output_dir()
        save_path = os.path.join(output_dir, f"{audio_basename}.wav")

        audio_response = requests.get(audio_url)
        audio_response.raise_for_status()  # 检查请求是否成功
        with open(save_path, 'wb') as f:
            f.write(audio_response.content)
        print(f"🔊 音频文件已保存至：{save_path}")
        return True
    except Exception as e:
        print(f"❌ 语音合成失败：{str(e)}")
        return False

if __name__ == "__main__":
    main()