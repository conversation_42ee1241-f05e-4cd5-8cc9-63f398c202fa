<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI图片生成与分析语音合成系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .main-content {
            padding: 40px;
        }

        .step {
            margin-bottom: 40px;
            padding: 30px;
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            transition: all 0.3s ease;
        }

        .step:hover {
            border-color: #4facfe;
            box-shadow: 0 5px 15px rgba(79, 172, 254, 0.2);
        }

        .step-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .step-number {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
        }

        .step-title {
            font-size: 1.5em;
            color: #333;
            font-weight: bold;
        }

        .input-group {
            margin-bottom: 20px;
        }

        .input-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #555;
        }

        .input-group input, .input-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .input-group input:focus, .input-group textarea:focus {
            outline: none;
            border-color: #4facfe;
        }

        .btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
        }

        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .btn-success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        }

        .btn-warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        .result-area {
            margin-top: 20px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #4facfe;
        }

        .image-preview {
            max-width: 100%;
            max-height: 400px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            margin: 20px 0;
        }

        .analysis-text {
            background: white;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #e0e0e0;
            line-height: 1.6;
            color: #333;
            white-space: pre-wrap;
        }

        .audio-player {
            width: 100%;
            margin: 20px 0;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .loading-spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #4facfe;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-pending { background: #ccc; }
        .status-processing { background: #ffc107; }
        .status-completed { background: #28a745; }
        .status-error { background: #dc3545; }

        .message {
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: bold;
        }

        .message-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .message-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .footer {
            background: #f8f9fa;
            padding: 20px;
            text-align: center;
            color: #666;
            border-top: 1px solid #e0e0e0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 AI图片生成与分析语音合成系统</h1>
            <p>图片生成 → 内容分析 → 语音合成</p>
        </div>

        <div class="main-content">
            <!-- 步骤1：生成图片 -->
            <div class="step" id="step1">
                <div class="step-header">
                    <div class="step-number">1</div>
                    <div class="step-title">
                        <span class="status-indicator status-pending" id="status1"></span>
                        生成图片
                    </div>
                </div>
                
                <div class="input-group">
                    <label for="prompt">请输入图片生成提示词：</label>
                    <textarea id="prompt" rows="3" placeholder="例如：美丽的夕阳风景、可爱的小猫、未来城市..."></textarea>
                </div>
                
                <button class="btn" id="generateBtn" onclick="generateImage()">🎨 生成图片</button>
                
                <div class="loading" id="loading1">
                    <div class="loading-spinner"></div>
                    <p>正在生成图片，请稍候...</p>
                </div>
                
                <div class="result-area" id="result1" style="display: none;">
                    <img class="image-preview" id="generatedImage" alt="生成的图片">
                </div>
            </div>

            <!-- 步骤2：分析图片 -->
            <div class="step" id="step2">
                <div class="step-header">
                    <div class="step-number">2</div>
                    <div class="step-title">
                        <span class="status-indicator status-pending" id="status2"></span>
                        理解图片
                    </div>
                </div>
                
                <p style="margin-bottom: 20px; color: #666;">使用AI分析生成的图片内容，提供详细的描述。</p>
                
                <button class="btn" id="analyzeBtn" onclick="analyzeImage()" disabled>🔍 理解图片</button>
                
                <div class="loading" id="loading2">
                    <div class="loading-spinner"></div>
                    <p>正在分析图片，请稍候...</p>
                </div>
                
                <div class="result-area" id="result2" style="display: none;">
                    <div class="analysis-text" id="analysisText"></div>
                </div>
            </div>

            <!-- 步骤3：生成语音 -->
            <div class="step" id="step3">
                <div class="step-header">
                    <div class="step-number">3</div>
                    <div class="step-title">
                        <span class="status-indicator status-pending" id="status3"></span>
                        生成语音
                    </div>
                </div>
                
                <p style="margin-bottom: 20px; color: #666;">将图片分析结果转换为语音，支持在线播放。</p>
                
                <button class="btn" id="speechBtn" onclick="generateSpeech()" disabled>🔊 生成语音</button>
                
                <div class="loading" id="loading3">
                    <div class="loading-spinner"></div>
                    <p>正在生成语音，请稍候...</p>
                </div>
                
                <div class="result-area" id="result3" style="display: none;">
                    <audio class="audio-player" id="audioPlayer" controls>
                        您的浏览器不支持音频播放。
                    </audio>
                    <button class="btn btn-success" onclick="playAudio()">▶️ 播放语音</button>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>🔧 技术栈：Gemini API + 通义千问 TTS | 💡 Powered by Flask</p>
        </div>
    </div>

    <script>
        let currentStep = 1;
        let generatedImagePath = '';
        let audioPath = '';

        // 生成图片
        async function generateImage() {
            const prompt = document.getElementById('prompt').value.trim();
            if (!prompt) {
                showMessage('请输入提示词', 'error');
                return;
            }

            setStepStatus(1, 'processing');
            showLoading(1, true);
            document.getElementById('generateBtn').disabled = true;

            try {
                const response = await fetch('/api/generate_image', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ prompt: prompt })
                });

                const data = await response.json();
                
                if (data.success) {
                    setStepStatus(1, 'completed');
                    document.getElementById('generatedImage').src = data.image_path;
                    document.getElementById('result1').style.display = 'block';
                    document.getElementById('analyzeBtn').disabled = false;
                    generatedImagePath = data.image_path;
                    showMessage(data.message, 'success');
                    currentStep = 2;
                } else {
                    setStepStatus(1, 'error');
                    showMessage(data.error, 'error');
                }
            } catch (error) {
                setStepStatus(1, 'error');
                showMessage('网络错误：' + error.message, 'error');
            } finally {
                showLoading(1, false);
                document.getElementById('generateBtn').disabled = false;
            }
        }

        // 分析图片
        async function analyzeImage() {
            setStepStatus(2, 'processing');
            showLoading(2, true);
            document.getElementById('analyzeBtn').disabled = true;

            try {
                const response = await fetch('/api/analyze_image', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const data = await response.json();
                
                if (data.success) {
                    setStepStatus(2, 'completed');
                    document.getElementById('analysisText').textContent = data.analysis;
                    document.getElementById('result2').style.display = 'block';
                    document.getElementById('speechBtn').disabled = false;
                    showMessage(data.message, 'success');
                    currentStep = 3;
                } else {
                    setStepStatus(2, 'error');
                    showMessage(data.error, 'error');
                }
            } catch (error) {
                setStepStatus(2, 'error');
                showMessage('网络错误：' + error.message, 'error');
            } finally {
                showLoading(2, false);
                document.getElementById('analyzeBtn').disabled = false;
            }
        }

        // 生成语音
        async function generateSpeech() {
            setStepStatus(3, 'processing');
            showLoading(3, true);
            document.getElementById('speechBtn').disabled = true;

            try {
                const response = await fetch('/api/generate_speech', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const data = await response.json();
                
                if (data.success) {
                    setStepStatus(3, 'completed');
                    document.getElementById('audioPlayer').src = data.audio_path;
                    document.getElementById('result3').style.display = 'block';
                    audioPath = data.audio_path;
                    showMessage(data.message + ` (文本长度: ${data.text_length} 字符)`, 'success');
                } else {
                    setStepStatus(3, 'error');
                    showMessage(data.error, 'error');
                }
            } catch (error) {
                setStepStatus(3, 'error');
                showMessage('网络错误：' + error.message, 'error');
            } finally {
                showLoading(3, false);
                document.getElementById('speechBtn').disabled = false;
            }
        }

        // 播放音频
        function playAudio() {
            const audio = document.getElementById('audioPlayer');
            if (audio.src) {
                audio.play();
            }
        }

        // 设置步骤状态
        function setStepStatus(step, status) {
            const statusElement = document.getElementById(`status${step}`);
            statusElement.className = `status-indicator status-${status}`;
        }

        // 显示/隐藏加载动画
        function showLoading(step, show) {
            const loadingElement = document.getElementById(`loading${step}`);
            loadingElement.style.display = show ? 'block' : 'none';
        }

        // 显示消息
        function showMessage(message, type) {
            // 移除之前的消息
            const existingMessages = document.querySelectorAll('.message');
            existingMessages.forEach(msg => msg.remove());

            // 创建新消息
            const messageDiv = document.createElement('div');
            messageDiv.className = `message message-${type}`;
            messageDiv.textContent = message;

            // 插入到主内容区域顶部
            const mainContent = document.querySelector('.main-content');
            mainContent.insertBefore(messageDiv, mainContent.firstChild);

            // 3秒后自动移除
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.remove();
                }
            }, 5000);
        }

        // 页面加载时检查状态
        window.onload = function() {
            checkStatus();
        };

        // 检查系统状态
        async function checkStatus() {
            try {
                const response = await fetch('/api/status');
                const data = await response.json();
                
                if (data.success) {
                    if (data.latest_image) {
                        setStepStatus(1, 'completed');
                        document.getElementById('generatedImage').src = `/static/images/${data.latest_image}`;
                        document.getElementById('result1').style.display = 'block';
                        document.getElementById('analyzeBtn').disabled = false;
                        generatedImagePath = `/static/images/${data.latest_image}`;
                    }
                    
                    if (data.analysis_file) {
                        setStepStatus(2, 'completed');
                        document.getElementById('speechBtn').disabled = false;
                    }
                    
                    if (data.audio_file) {
                        setStepStatus(3, 'completed');
                        document.getElementById('audioPlayer').src = `/static/audio/${data.audio_file}`;
                        document.getElementById('result3').style.display = 'block';
                        audioPath = `/static/audio/${data.audio_file}`;
                    }
                }
            } catch (error) {
                console.log('检查状态失败:', error);
            }
        }
    </script>
</body>
</html>
