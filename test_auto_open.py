"""
测试自动打开图片功能
"""

from dotenv import load_dotenv
from image_gen import GeminiImageGenerator

# 加载环境变量
load_dotenv()

def test_auto_open():
    """测试自动打开功能"""
    print("=== 测试自动打开图片功能 ===")
    
    try:
        # 创建生成器
        generator = GeminiImageGenerator()
        
        # 生成图像并自动打开
        print("正在生成图像（将自动打开）...")
        result = generator.generate_image(
            prompt="一朵美丽的玫瑰花，红色花瓣，绿色叶子，写实风格",
            filename="beautiful_rose",
            auto_open=True  # 自动打开
        )
        
        print(f"✅ 生成成功！文件已保存并打开")
        print(f"保存的文件: {result['files_saved']}")
        
        # 再生成一张但不自动打开
        print("\n正在生成第二张图像（不自动打开）...")
        result2 = generator.generate_image(
            prompt="一只蝴蝶在花丛中飞舞，彩色翅膀，春天的感觉",
            filename="butterfly",
            auto_open=False  # 不自动打开
        )
        
        print(f"✅ 第二张图像生成成功！")
        print(f"保存的文件: {result2['files_saved']}")
        print("注意：第二张图像没有自动打开")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_auto_open()
