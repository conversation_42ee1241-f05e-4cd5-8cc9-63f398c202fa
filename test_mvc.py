"""
测试MVC架构的基本功能
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试导入功能"""
    print("🧪 测试MVC架构导入...")
    
    try:
        print("📦 测试配置模块...")
        from app.config.settings import Config
        print("✅ Config 导入成功")
        
        print("📦 测试工具模块...")
        from app.utils.dependencies import check_dependencies
        print("✅ Dependencies 导入成功")
        
        print("📦 测试模型模块...")
        # 这些可能会因为缺少依赖而失败，但我们可以捕获异常
        try:
            from app.models.image_generator import GeminiImageGenerator
            print("✅ ImageGenerator 导入成功")
        except Exception as e:
            print(f"⚠️ ImageGenerator 导入失败: {e}")
        
        try:
            from app.models.image_analyzer import ImageAnalyzer
            print("✅ ImageAnalyzer 导入成功")
        except Exception as e:
            print(f"⚠️ ImageAnalyzer 导入失败: {e}")
        
        try:
            from app.models.speech_synthesizer import SpeechSynthesizer
            print("✅ SpeechSynthesizer 导入成功")
        except Exception as e:
            print(f"⚠️ SpeechSynthesizer 导入失败: {e}")
        
        print("📦 测试控制器模块...")
        try:
            from app.controllers.main_controller import MainController
            print("✅ MainController 导入成功")
        except Exception as e:
            print(f"⚠️ MainController 导入失败: {e}")
        
        print("\n🎉 MVC架构基本结构测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 导入测试失败: {e}")
        return False

def test_config():
    """测试配置功能"""
    print("\n🔧 测试配置功能...")
    
    try:
        from app.config.settings import Config
        
        # 测试基本配置获取
        api_key = Config.get_api_key()
        print(f"📋 API Key: {api_key[:10]}..." if api_key else "📋 API Key: 未设置")
        
        output_dir = Config.get_output_dir()
        print(f"📁 输出目录: {output_dir}")
        
        flask_port = Config.get_flask_port()
        print(f"🌐 Flask端口: {flask_port}")
        
        print("✅ 配置功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def test_directory_structure():
    """测试目录结构"""
    print("\n📁 测试目录结构...")
    
    required_dirs = [
        'app',
        'app/models',
        'app/controllers', 
        'app/config',
        'app/utils'
    ]
    
    required_files = [
        'app/__init__.py',
        'app/app.py',
        'app/main_cli.py',
        'app/models/__init__.py',
        'app/models/image_generator.py',
        'app/models/image_analyzer.py',
        'app/models/speech_synthesizer.py',
        'app/controllers/__init__.py',
        'app/controllers/main_controller.py',
        'app/config/__init__.py',
        'app/config/settings.py',
        'app/utils/__init__.py',
        'app/utils/dependencies.py',
        'run.py',
        'PROJECT_STRUCTURE.md'
    ]
    
    all_good = True
    
    # 检查目录
    for dir_path in required_dirs:
        if os.path.exists(dir_path) and os.path.isdir(dir_path):
            print(f"✅ 目录存在: {dir_path}")
        else:
            print(f"❌ 目录缺失: {dir_path}")
            all_good = False
    
    # 检查文件
    for file_path in required_files:
        if os.path.exists(file_path) and os.path.isfile(file_path):
            print(f"✅ 文件存在: {file_path}")
        else:
            print(f"❌ 文件缺失: {file_path}")
            all_good = False
    
    if all_good:
        print("🎉 目录结构完整！")
    else:
        print("⚠️ 目录结构不完整")
    
    return all_good

def main():
    """主测试函数"""
    print("🧪 MVC架构测试工具")
    print("=" * 50)
    
    # 运行所有测试
    tests = [
        ("目录结构", test_directory_structure),
        ("配置功能", test_config),
        ("模块导入", test_imports)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 运行测试: {test_name}")
        print("-" * 30)
        result = test_func()
        results.append((test_name, result))
    
    # 汇总结果
    print("\n📊 测试结果汇总")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！MVC架构配置正确。")
    else:
        print("⚠️ 部分测试失败，请检查配置。")

if __name__ == "__main__":
    main()
