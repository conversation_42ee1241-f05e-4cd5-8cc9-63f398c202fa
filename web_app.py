#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Web应用：AI图片生成与分析语音合成系统
提供图形界面操作整个功能流程
"""

import os
import json
import glob
from flask import Flask, render_template, request, jsonify, send_file, url_for
from werkzeug.utils import secure_filename
from dotenv import load_dotenv

# 导入我们的功能模块
from image_gen import GeminiImageGenerator, Config
from image_understand import main as analyze_main, get_latest_image, analyze_image
from speaker import main as speaker_main, get_latest_analysis_file, extract_analysis_content
from google import genai

# 加载环境变量
load_dotenv()

app = Flask(__name__)
app.secret_key = os.urandom(24)

# 配置上传文件夹
UPLOAD_FOLDER = 'generated_images'
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER

# 确保上传文件夹存在
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

@app.route('/')
def index():
    """主页面"""
    return render_template('index.html')

@app.route('/api/generate_image', methods=['POST'])
def generate_image():
    """生成图片API"""
    try:
        data = request.get_json()
        prompt = data.get('prompt', '').strip()

        if not prompt:
            return jsonify({'success': False, 'error': '提示词不能为空'})

        # 生成图片
        generator = GeminiImageGenerator()
        result = generator.generate_image(prompt, show_proxy_status=False)

        if result:
            # 获取最新生成的图片
            latest_image = get_latest_image()
            image_filename = os.path.basename(latest_image)

            return jsonify({
                'success': True,
                'message': '图片生成成功！',
                'image_path': f'/static/images/{image_filename}',
                'image_filename': image_filename
            })
        else:
            return jsonify({'success': False, 'error': '图片生成失败'})

    except Exception as e:
        return jsonify({'success': False, 'error': f'生成图片时出错：{str(e)}'})

@app.route('/api/check_latest_image')
def check_latest_image():
    """检查最新图片API - 用于实时更新"""
    try:
        # 获取最新图片
        output_dir = Config.get_output_dir()
        image_pattern = os.path.join(output_dir, f"{Config.get_output_prefix()}-*.{Config.get_image_format()}")
        image_files = glob.glob(image_pattern)

        if image_files:
            latest_image = max(image_files, key=os.path.getctime)
            image_filename = os.path.basename(latest_image)

            return jsonify({
                'success': True,
                'has_image': True,
                'image_path': f'/static/images/{image_filename}',
                'image_filename': image_filename,
                'timestamp': os.path.getctime(latest_image)
            })
        else:
            return jsonify({
                'success': True,
                'has_image': False
            })

    except Exception as e:
        return jsonify({'success': False, 'error': f'检查图片时出错：{str(e)}'})

@app.route('/api/analyze_image', methods=['POST'])
def analyze_image_api():
    """分析图片API"""
    try:
        # 获取最新图片
        latest_image = get_latest_image()
        
        # 初始化Gemini客户端
        client = genai.Client(api_key=Config.get_api_key())
        
        # 分析图片
        from image_understand import analyze_image
        result = analyze_image(client, latest_image)
        
        if result:
            # 保存分析结果
            image_dir = os.path.dirname(latest_image)
            image_basename = os.path.splitext(os.path.basename(latest_image))[0]
            result_filename = os.path.join(image_dir, f"{image_basename}_analysis.txt")
            
            import datetime
            with open(result_filename, 'w', encoding='utf-8') as f:
                creation_time = datetime.datetime.fromtimestamp(os.path.getctime(latest_image))
                analysis_time = datetime.datetime.now()
                
                f.write(f"图片文件: {os.path.basename(latest_image)}\n")
                f.write(f"图片创建时间: {creation_time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"分析时间: {analysis_time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write("-" * 50 + "\n")
                f.write(result)
            
            return jsonify({
                'success': True,
                'message': '图片分析完成！',
                'analysis': result,
                'analysis_file': f"{image_basename}_analysis.txt"
            })
        else:
            return jsonify({'success': False, 'error': '图片分析失败'})
            
    except Exception as e:
        return jsonify({'success': False, 'error': f'分析图片时出错：{str(e)}'})

@app.route('/api/generate_speech', methods=['POST'])
def generate_speech():
    """生成语音API"""
    try:
        import dashscope
        import requests
        
        # 获取最新分析文件
        latest_analysis_file = get_latest_analysis_file()
        full_text = extract_analysis_content(latest_analysis_file)
        
        # 处理文本长度
        max_length = int(os.getenv("TTS_MAX_LENGTH", "500"))
        if len(full_text) > max_length:
            text = full_text[:max_length]
            last_period = text.rfind('。')
            if last_period > max_length // 2:
                text = text[:last_period + 1]
        else:
            text = full_text
        
        # 调用TTS API
        response = dashscope.audio.qwen_tts.SpeechSynthesizer.call(
            model=os.getenv("TTS_MODEL", "qwen-tts-latest"),
            api_key=os.getenv("DASHSCOPE_API_KEY"),
            text=text,
            voice=os.getenv("TTS_VOICE", "Ethan"),
        )
        
        if response.output and response.output.audio:
            audio_url = response.output.audio["url"]
            
            # 下载音频文件
            audio_response = requests.get(audio_url)
            if audio_response.status_code == 200:
                # 保存音频文件
                analysis_basename = os.path.splitext(os.path.basename(latest_analysis_file))[0]
                audio_basename = analysis_basename.replace('_analysis', '_audio')
                save_path = os.path.join(Config.get_output_dir(), f"{audio_basename}.wav")
                
                with open(save_path, 'wb') as f:
                    f.write(audio_response.content)
                
                return jsonify({
                    'success': True,
                    'message': '语音生成成功！',
                    'audio_path': f'/static/audio/{audio_basename}.wav',
                    'audio_filename': f"{audio_basename}.wav",
                    'text_length': len(text)
                })
            else:
                return jsonify({'success': False, 'error': f'下载音频失败，状态码：{audio_response.status_code}'})
        else:
            return jsonify({'success': False, 'error': '语音合成失败'})
            
    except Exception as e:
        return jsonify({'success': False, 'error': f'生成语音时出错：{str(e)}'})

@app.route('/static/images/<filename>')
def serve_image(filename):
    """提供图片文件"""
    try:
        file_path = os.path.join(Config.get_output_dir(), filename)
        if os.path.exists(file_path):
            return send_file(file_path)
        else:
            return "文件不存在", 404
    except Exception as e:
        return f"服务图片时出错：{str(e)}", 500

@app.route('/static/audio/<filename>')
def serve_audio(filename):
    """提供音频文件"""
    try:
        file_path = os.path.join(Config.get_output_dir(), filename)
        if os.path.exists(file_path):
            return send_file(file_path)
        else:
            return "文件不存在", 404
    except Exception as e:
        return f"服务音频时出错：{str(e)}", 500

@app.route('/api/status')
def get_status():
    """获取系统状态"""
    try:
        # 检查最新文件
        output_dir = Config.get_output_dir()
        
        # 查找最新图片
        image_pattern = os.path.join(output_dir, f"{Config.get_output_prefix()}-*.{Config.get_image_format()}")
        image_files = glob.glob(image_pattern)
        latest_image = max(image_files, key=os.path.getctime) if image_files else None
        
        # 查找对应的分析文件和音频文件
        analysis_file = None
        audio_file = None
        
        if latest_image:
            image_basename = os.path.splitext(os.path.basename(latest_image))[0]
            analysis_path = os.path.join(output_dir, f"{image_basename}_analysis.txt")
            audio_path = os.path.join(output_dir, f"{image_basename}_audio.wav")
            
            analysis_file = f"{image_basename}_analysis.txt" if os.path.exists(analysis_path) else None
            audio_file = f"{image_basename}_audio.wav" if os.path.exists(audio_path) else None
        
        return jsonify({
            'success': True,
            'latest_image': os.path.basename(latest_image) if latest_image else None,
            'analysis_file': analysis_file,
            'audio_file': audio_file
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': f'获取状态时出错：{str(e)}'})

if __name__ == '__main__':
    print("🌐 启动Web应用...")
    print("📱 访问地址：http://localhost:5000")
    print("🎨 AI图片生成与分析语音合成系统")
    app.run(debug=True, host='0.0.0.0', port=5000)
